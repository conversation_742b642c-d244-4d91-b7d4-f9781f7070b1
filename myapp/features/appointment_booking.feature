Feature: Appointment Booking
  As a patient
  I want to search for doctors and book appointments
  So that I can receive medical consultation

  Background:
    Given the system has verified doctors available
    And I am a registered and verified patient

  @api
  Scenario: Patient successfully books an appointment with a doctor
    Given I am logged in as a patient
    When I search for doctors by specialization "Gynecology"
    Then I should see a list of available doctors
    And each doctor should have consultation fees displayed
    
    When I select a doctor from the search results
    Then I should see the doctor's detailed profile
    And I should see their available time slots
    
    When I select an available time slot for tomorrow
    And I provide appointment details:
      | complaint_summary | I have been experiencing irregular periods |
      | appointment_type  | consultation                               |
    And I confirm the appointment booking
    Then the appointment should be created successfully
    And I should receive a booking confirmation
    And the doctor should be notified of the new appointment
    
    When I check my appointments
    Then I should see the newly booked appointment in my list
    And the appointment status should be "pending"

  @api
  Scenario: Patient searches for doctors with filters
    Given I am logged in as a patient
    When I search for doctors with the following filters:
      | specialization | Gynecology |
      | min_rating     | 4.0        |
      | max_fee        | 500        |
    Then I should see only doctors matching the criteria
    And all doctors should have rating of 4.0 or higher
    And all doctors should have consultation fees of 500 TL or less

  @api
  Scenario: Patient cannot book appointment without authentication
    Given I am not logged in
    When I try to book an appointment
    Then I should receive an authentication error
    And the appointment should not be created

  @api
  Scenario: Patient views appointment details
    Given I am logged in as a patient
    And I have an existing appointment
    When I view my appointment details
    Then I should see all appointment information:
      | doctor_name       |
      | appointment_date  |
      | appointment_time  |
      | consultation_fee  |
      | appointment_type  |
      | status           |
    And I should see options to cancel or reschedule if applicable
