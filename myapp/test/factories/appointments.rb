FactoryBot.define do
  factory :appointment do
    association :patient
    association :doctor
    appointment_date { Date.tomorrow }
    appointment_time { "10:00" }
    appointment_type { "consultation" }
    complaint_summary { "Regular checkup" }
    status { :pending }
    consultation_fee { 200.0 }
    sequence(:confirmation_number) { |n| "APT#{n.to_s.rjust(8, '0')}" }

    trait :confirmed do
      status { :confirmed }
      confirmed_at { 1.hour.ago }
    end

    trait :completed do
      status { :completed }
      confirmed_at { 2.hours.ago }
      started_at { 1.hour.ago }
      completed_at { 30.minutes.ago }
    end

    trait :cancelled do
      status { :cancelled }
      cancelled_at { 1.hour.ago }
      cancellation_reason { "Patient request" }
    end

    trait :emergency do
      appointment_type { "emergency" }
      consultation_fee { 500.0 }
      complaint_summary { "Urgent medical attention needed" }
    end

    trait :follow_up do
      appointment_type { "follow_up" }
      complaint_summary { "Follow-up visit" }
    end

    trait :today do
      appointment_date { Date.current }
    end

    trait :next_week do
      appointment_date { 1.week.from_now.to_date }
    end
  end
end
