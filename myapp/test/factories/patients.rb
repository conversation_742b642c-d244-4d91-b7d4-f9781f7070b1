FactoryBot.define do
  factory :patient do
    association :user
    emergency_contact_name { "<PERSON>" }
    emergency_contact_phone { "+905551234568" }
    emergency_contact_relationship { "spouse" }
    blood_type { "A+" }
    height { 165.0 }
    weight { 60.0 }
    allergies { ["Penicillin"] }
    chronic_conditions { ["Hypertension"] }
    current_medications { ["Lisinopril 10mg"] }
    smoking_status { :non_smoker }
    alcohol_consumption { "occasional" }
    exercise_frequency { "weekly" }
    last_checkup_date { 6.months.ago.to_date }

    trait :with_health_profile do
      after(:create) do |patient|
        create(:health_profile, patient: patient)
      end
    end

    trait :smoker do
      smoking_status { :current_smoker }
    end

    trait :diabetic do
      chronic_conditions { ["Diabetes Type 2"] }
      current_medications { ["Metformin 500mg"] }
    end
  end
end
