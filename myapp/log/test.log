  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE IF EXISTS "admins"[0m
  [1m[35m (2.8ms)[0m  [1m[35mCREATE TABLE "admins" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "role" varchar DEFAULT 'admin' NOT NULL, "permissions" text, "department" varchar NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_admins_on_role" ON "admins" ("role")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_admins_on_user_id" ON "admins" ("user_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE IF EXISTS "ai_health_insights"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "ai_health_insights" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "insight_type" varchar NOT NULL, "title" varchar NOT NULL, "content" text NOT NULL, "summary" text, "confidence_score" decimal(5,4) NOT NULL, "generated_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "priority" integer DEFAULT 1, "data_sources" json, "recommendations" json, "metadata" json, "ai_model_version" varchar, "reviewed_by_doctor" boolean DEFAULT 0, "reviewed_by_id" integer, "reviewed_at" datetime(6), "doctor_notes" text, "patient_acknowledged" boolean DEFAULT 0, "acknowledged_at" datetime(6), "expires_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_confidence_score" ON "ai_health_insights" ("confidence_score")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_generated_at" ON "ai_health_insights" ("generated_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_insight_type" ON "ai_health_insights" ("insight_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_patient_id_and_generated_at" ON "ai_health_insights" ("patient_id", "generated_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_patient_id" ON "ai_health_insights" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_priority" ON "ai_health_insights" ("priority")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_reviewed_by_doctor" ON "ai_health_insights" ("reviewed_by_doctor")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_reviewed_by_id" ON "ai_health_insights" ("reviewed_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_status" ON "ai_health_insights" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "appointments"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "appointments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "appointment_type" varchar NOT NULL, "scheduled_at" datetime(6) NOT NULL, "ends_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "duration_minutes" integer DEFAULT 30 NOT NULL, "consultation_fee" decimal(8,2) NOT NULL, "patient_notes" text, "doctor_notes" text, "meeting_link" varchar, "meeting_id" varchar, "started_at" datetime(6), "ended_at" datetime(6), "cancellation_reason" varchar, "cancelled_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_appointment_type" ON "appointments" ("appointment_type")[0m
  [1m[35m (0.7ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_doctor_id_and_scheduled_at" ON "appointments" ("doctor_id", "scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_doctor_id" ON "appointments" ("doctor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_patient_id_and_scheduled_at" ON "appointments" ("patient_id", "scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_patient_id" ON "appointments" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_scheduled_at" ON "appointments" ("scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_status" ON "appointments" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "attachments"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "attachments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "attachable_type" varchar NOT NULL, "attachable_id" integer NOT NULL, "user_id" integer NOT NULL, "file_name" varchar NOT NULL, "original_file_name" varchar NOT NULL, "file_url" varchar NOT NULL, "file_type" varchar NOT NULL, "file_size_bytes" bigint NOT NULL, "file_hash" varchar, "mime_type" varchar, "description" text, "is_public" boolean DEFAULT 0, "is_processed" boolean DEFAULT 0, "processing_metadata" json, "thumbnail_url" varchar, "uploaded_at" datetime(6) NOT NULL, "last_accessed_at" datetime(6), "download_count" integer DEFAULT 0, "expires_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_attachable" ON "attachments" ("attachable_type", "attachable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_attachable_type_and_attachable_id" ON "attachments" ("attachable_type", "attachable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_attachments_on_file_hash" ON "attachments" ("file_hash")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_file_type" ON "attachments" ("file_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_is_public" ON "attachments" ("is_public")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_uploaded_at" ON "attachments" ("uploaded_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_user_id" ON "attachments" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "calendar_integrations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "calendar_integrations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "provider" varchar NOT NULL, "provider_user_id" varchar, "access_token" varchar NOT NULL, "refresh_token" varchar, "calendar_id" varchar, "calendar_name" varchar, "token_expires_at" datetime(6), "active" boolean DEFAULT 1, "sync_appointments" boolean DEFAULT 1, "sync_reminders" boolean DEFAULT 1, "last_sync_at" datetime(6), "sync_settings" json, "provider_metadata" json, "sync_errors" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_active" ON "calendar_integrations" ("active")[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_last_sync_at" ON "calendar_integrations" ("last_sync_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_provider" ON "calendar_integrations" ("provider")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_calendar_integrations_on_user_id_and_provider" ON "calendar_integrations" ("user_id", "provider")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_user_id" ON "calendar_integrations" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "consultations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "consultations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "appointment_id" integer, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "consultation_type" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "started_at" datetime(6), "ended_at" datetime(6), "duration_minutes" integer, "chief_complaint" text NOT NULL, "history_of_present_illness" text, "physical_examination" text, "assessment" text, "plan" text, "follow_up_instructions" text, "additional_notes" text, "consultation_fee" decimal(8,2), "consultation_method" varchar, "vital_signs" json, "symptoms" json, "prescription_issued" boolean DEFAULT 0, "next_follow_up_date" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_appointment_id" ON "consultations" ("appointment_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_consultation_type" ON "consultations" ("consultation_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_doctor_id_and_started_at" ON "consultations" ("doctor_id", "started_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_doctor_id" ON "consultations" ("doctor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_next_follow_up_date" ON "consultations" ("next_follow_up_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_patient_id_and_started_at" ON "consultations" ("patient_id", "started_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_patient_id" ON "consultations" ("patient_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_started_at" ON "consultations" ("started_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_status" ON "consultations" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "course_enrollments"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "course_enrollments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_id" integer NOT NULL, "patient_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "enrolled_at" datetime(6) NOT NULL, "completed_at" datetime(6), "last_accessed_at" datetime(6), "progress_percentage" decimal(5,2) DEFAULT 0.0, "total_watch_time_seconds" integer DEFAULT 0, "rating" decimal(3,2), "review" text, "review_submitted_at" datetime(6), "certificate_issued" boolean DEFAULT 0, "certificate_issued_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_completed_at" ON "course_enrollments" ("completed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_course_enrollments_on_course_id_and_patient_id" ON "course_enrollments" ("course_id", "patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_course_id" ON "course_enrollments" ("course_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_enrolled_at" ON "course_enrollments" ("enrolled_at")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_patient_id" ON "course_enrollments" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_progress_percentage" ON "course_enrollments" ("progress_percentage")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_status" ON "course_enrollments" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "course_modules"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "course_modules" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_id" integer NOT NULL, "title" varchar NOT NULL, "description" text, "sort_order" integer NOT NULL, "duration_minutes" integer DEFAULT 0, "is_published" boolean DEFAULT 0, "learning_objectives" json, "content_summary" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_course_modules_on_course_id_and_sort_order" ON "course_modules" ("course_id", "sort_order")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_modules_on_course_id" ON "course_modules" ("course_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_modules_on_is_published" ON "course_modules" ("is_published")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "course_videos"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "course_videos" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_module_id" integer NOT NULL, "title" varchar NOT NULL, "description" text, "video_url" varchar NOT NULL, "thumbnail_url" varchar, "duration_seconds" integer NOT NULL, "sort_order" integer NOT NULL, "is_published" boolean DEFAULT 0, "is_preview" boolean DEFAULT 0, "video_quality" varchar, "file_size_bytes" bigint, "subtitles" json, "video_metadata" json, "view_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_course_videos_on_course_module_id_and_sort_order" ON "course_videos" ("course_module_id", "sort_order")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_videos_on_course_module_id" ON "course_videos" ("course_module_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_videos_on_is_preview" ON "course_videos" ("is_preview")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_videos_on_is_published" ON "course_videos" ("is_published")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "courses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "courses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "instructor_id" integer NOT NULL, "title" varchar NOT NULL, "description" text NOT NULL, "slug" varchar NOT NULL, "category" varchar NOT NULL, "difficulty_level" varchar NOT NULL, "duration_minutes" integer DEFAULT 0, "price" decimal(8,2) DEFAULT 0.0, "currency" varchar DEFAULT 'TRY', "status" integer DEFAULT 0 NOT NULL, "published_at" datetime(6), "thumbnail_url" varchar, "preview_video_url" varchar, "learning_objectives" json, "prerequisites" json, "target_audience" json, "enrollment_count" integer DEFAULT 0, "rating" decimal(3,2), "review_count" integer DEFAULT 0, "is_featured" boolean DEFAULT 0, "sort_order" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_category" ON "courses" ("category")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_difficulty_level" ON "courses" ("difficulty_level")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_instructor_id_and_status" ON "courses" ("instructor_id", "status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_instructor_id" ON "courses" ("instructor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_is_featured" ON "courses" ("is_featured")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_published_at" ON "courses" ("published_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_rating" ON "courses" ("rating")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_courses_on_slug" ON "courses" ("slug")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_status" ON "courses" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "cycle_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "cycle_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "cycle_start_date" date NOT NULL, "cycle_end_date" date, "predicted_next_cycle" date, "cycle_length" integer, "period_length" integer, "flow_intensity" varchar, "symptoms" json, "mood_data" json, "notes" text, "is_irregular" boolean DEFAULT 0, "basal_body_temperature" decimal(4,2), "cervical_mucus_type" varchar, "ovulation_detected" boolean DEFAULT 0, "ovulation_date" date, "fertility_signs" json, "contraception_used" boolean DEFAULT 0, "contraception_type" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_cycle_start_date" ON "cycle_trackers" ("cycle_start_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_ovulation_date" ON "cycle_trackers" ("ovulation_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_patient_id_and_cycle_start_date" ON "cycle_trackers" ("patient_id", "cycle_start_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_patient_id" ON "cycle_trackers" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_predicted_next_cycle" ON "cycle_trackers" ("predicted_next_cycle")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "doctor_availabilities"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "doctor_availabilities" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "day_of_week" integer NOT NULL, "start_time" varchar NOT NULL, "end_time" varchar NOT NULL, "appointment_duration" integer DEFAULT 30 NOT NULL, "buffer_time" integer DEFAULT 5, "max_appointments" integer DEFAULT 16 NOT NULL, "is_available" boolean DEFAULT 1, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctor_availabilities_on_day_of_week" ON "doctor_availabilities" ("day_of_week")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_doctor_availabilities_on_doctor_id_and_day_of_week" ON "doctor_availabilities" ("doctor_id", "day_of_week")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_doctor_availabilities_on_doctor_id" ON "doctor_availabilities" ("doctor_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_doctor_availabilities_on_is_available" ON "doctor_availabilities" ("is_available")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "doctor_verifications"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "doctor_verifications" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "verification_type" varchar, "status" integer, "submitted_at" datetime(6), "reviewed_at" datetime(6), "reviewer_notes" text, "documents" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctor_verifications_on_doctor_id" ON "doctor_verifications" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "doctors"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "doctors" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "license_number" varchar NOT NULL, "specialization" varchar NOT NULL, "sub_specializations" text, "graduated_university" varchar NOT NULL, "graduation_year" integer NOT NULL, "current_institution" varchar, "years_experience" integer DEFAULT 0, "consultation_fee_day" decimal(8,2) NOT NULL, "consultation_fee_night" decimal(8,2) NOT NULL, "consultation_fee_emergency" decimal(8,2) NOT NULL, "languages_spoken" text, "bio" text, "verification_status" integer DEFAULT 0, "rating" decimal(3,2), "total_consultations" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_doctors_on_license_number" ON "doctors" ("license_number")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_rating" ON "doctors" ("rating")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_specialization" ON "doctors" ("specialization")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_user_id" ON "doctors" ("user_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_verification_status" ON "doctors" ("verification_status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "group_sessions"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "group_sessions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "host_id" integer NOT NULL, "title" varchar NOT NULL, "description" text NOT NULL, "session_type" varchar NOT NULL, "scheduled_at" datetime(6) NOT NULL, "ends_at" datetime(6) NOT NULL, "duration_minutes" integer NOT NULL, "max_participants" integer DEFAULT 10, "current_participants" integer DEFAULT 0, "status" integer DEFAULT 0 NOT NULL, "price" decimal(8,2) DEFAULT 0.0, "currency" varchar DEFAULT 'TRY', "meeting_link" varchar, "meeting_id" varchar, "meeting_password" varchar, "started_at" datetime(6), "ended_at" datetime(6), "session_notes" text, "materials" json, "recording_enabled" boolean DEFAULT 0, "recording_url" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_host_id_and_scheduled_at" ON "group_sessions" ("host_id", "scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_host_id" ON "group_sessions" ("host_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_scheduled_at" ON "group_sessions" ("scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_session_type" ON "group_sessions" ("session_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_status" ON "group_sessions" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "health_metrics"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "health_metrics" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "trackable_type" varchar, "trackable_id" integer, "metric_type" varchar NOT NULL, "value" decimal(10,4) NOT NULL, "unit" varchar NOT NULL, "recorded_at" datetime(6) NOT NULL, "source" varchar NOT NULL, "notes" text, "metadata" json, "device_id" varchar, "reference_min" decimal(10,4), "reference_max" decimal(10,4), "is_abnormal" boolean DEFAULT 0, "trend" varchar, "quality_score" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_is_abnormal" ON "health_metrics" ("is_abnormal")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_metric_type" ON "health_metrics" ("metric_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "idx_on_patient_id_metric_type_recorded_at_d2a57a400b" ON "health_metrics" ("patient_id", "metric_type", "recorded_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_patient_id" ON "health_metrics" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_recorded_at" ON "health_metrics" ("recorded_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_source" ON "health_metrics" ("source")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_trackable" ON "health_metrics" ("trackable_type", "trackable_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_trackable_type_and_trackable_id" ON "health_metrics" ("trackable_type", "trackable_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "health_profiles"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "health_profiles" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "height" decimal, "weight" decimal, "allergies" text, "chronic_conditions" text, "medications" text, "family_history" text, "lifestyle_notes" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_profiles_on_patient_id" ON "health_profiles" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "medical_documents"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "medical_documents" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "issued_by_id" integer, "consultation_id" integer, "document_type" varchar NOT NULL, "title" varchar NOT NULL, "description" text, "file_url" varchar NOT NULL, "file_name" varchar, "file_type" varchar, "file_size_bytes" bigint, "file_hash" varchar, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6), "status" integer DEFAULT 0 NOT NULL, "is_sensitive" boolean DEFAULT 0, "access_permissions" json, "metadata" json, "external_id" varchar, "institution_name" varchar, "last_accessed_at" datetime(6), "access_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_consultation_id" ON "medical_documents" ("consultation_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_document_type" ON "medical_documents" ("document_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_expires_at" ON "medical_documents" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_medical_documents_on_file_hash" ON "medical_documents" ("file_hash")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_is_sensitive" ON "medical_documents" ("is_sensitive")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_issued_at" ON "medical_documents" ("issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_issued_by_id" ON "medical_documents" ("issued_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_patient_id_and_document_type" ON "medical_documents" ("patient_id", "document_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_patient_id" ON "medical_documents" ("patient_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_status" ON "medical_documents" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "medical_licenses"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "medical_licenses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "license_number" varchar NOT NULL, "issuing_authority" varchar NOT NULL, "issue_date" date NOT NULL, "expiry_date" date NOT NULL, "license_type" varchar NOT NULL, "status" integer DEFAULT 0, "document_url" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_doctor_id" ON "medical_licenses" ("doctor_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_expiry_date" ON "medical_licenses" ("expiry_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_medical_licenses_on_license_number" ON "medical_licenses" ("license_number")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_license_type" ON "medical_licenses" ("license_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_status" ON "medical_licenses" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "membership_features"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "membership_features" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "feature_key" varchar NOT NULL, "description" text NOT NULL, "feature_type" integer DEFAULT 0 NOT NULL, "category" varchar NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_features_on_category" ON "membership_features" ("category")[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_membership_features_on_feature_key" ON "membership_features" ("feature_key")[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_membership_features_on_feature_type" ON "membership_features" ("feature_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE IF EXISTS "membership_tiers"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "membership_tiers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "slug" varchar NOT NULL, "description" text NOT NULL, "price" decimal(8,2) DEFAULT 0.0 NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "billing_cycle" integer DEFAULT 1 NOT NULL, "trial_days" integer DEFAULT 0, "sort_order" integer NOT NULL, "active" boolean DEFAULT 1, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_tiers_on_active" ON "membership_tiers" ("active")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_tiers_on_price" ON "membership_tiers" ("price")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_membership_tiers_on_slug" ON "membership_tiers" ("slug")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_membership_tiers_on_sort_order" ON "membership_tiers" ("sort_order")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "notifications"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "notifications" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "notifiable_type" varchar, "notifiable_id" integer, "notification_type" varchar NOT NULL, "title" varchar NOT NULL, "message" text NOT NULL, "action_text" text, "action_url" varchar, "read_at" datetime(6), "sent_at" datetime(6) NOT NULL, "priority" integer DEFAULT 1 NOT NULL, "delivery_method" varchar, "delivered" boolean DEFAULT 0, "delivered_at" datetime(6), "delivery_metadata" json, "expires_at" datetime(6), "icon" varchar, "color" varchar, "data" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_delivered" ON "notifications" ("delivered")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_expires_at" ON "notifications" ("expires_at")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_notifiable" ON "notifications" ("notifiable_type", "notifiable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_notifiable_type_and_notifiable_id" ON "notifications" ("notifiable_type", "notifiable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_notification_type" ON "notifications" ("notification_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_priority" ON "notifications" ("priority")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_read_at" ON "notifications" ("read_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_sent_at" ON "notifications" ("sent_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_user_id_and_read_at" ON "notifications" ("user_id", "read_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_user_id" ON "notifications" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "order_items"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "order_items" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order_id" integer NOT NULL, "orderable_type" varchar NOT NULL, "orderable_id" integer NOT NULL, "quantity" integer DEFAULT 1 NOT NULL, "unit_price" decimal(10,2) NOT NULL, "total_price" decimal(10,2) NOT NULL, "item_name" varchar NOT NULL, "item_description" text, "item_metadata" json, "discount_amount" decimal(10,2) DEFAULT 0.0, "discount_type" varchar, "is_refundable" boolean DEFAULT 1, "delivered_at" datetime(6), "status" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_order_id" ON "order_items" ("order_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_orderable" ON "order_items" ("orderable_type", "orderable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_orderable_type_and_orderable_id" ON "order_items" ("orderable_type", "orderable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_status" ON "order_items" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "orders"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "orders" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "order_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "subtotal_amount" decimal(10,2) NOT NULL, "tax_amount" decimal(10,2) DEFAULT 0.0, "discount_amount" decimal(10,2) DEFAULT 0.0, "total_amount" decimal(10,2) NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "order_type" varchar, "billing_address" json, "discount_code" varchar, "notes" text, "confirmed_at" datetime(6), "shipped_at" datetime(6), "delivered_at" datetime(6), "cancelled_at" datetime(6), "cancellation_reason" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_orders_on_confirmed_at" ON "orders" ("confirmed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_orders_on_order_number" ON "orders" ("order_number")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_orders_on_order_type" ON "orders" ("order_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_orders_on_status" ON "orders" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_orders_on_user_id_and_status" ON "orders" ("user_id", "status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_orders_on_user_id" ON "orders" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "patients"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "patients" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "medical_record_number" varchar NOT NULL, "emergency_contact_name" varchar NOT NULL, "emergency_contact_phone" varchar NOT NULL, "blood_type" varchar, "pregnancy_count" integer DEFAULT 0, "birth_count" integer DEFAULT 0, "smoking_status" integer DEFAULT 0, "alcohol_consumption" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_patients_on_blood_type" ON "patients" ("blood_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_patients_on_medical_record_number" ON "patients" ("medical_record_number")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_patients_on_user_id" ON "patients" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "payment_methods"[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE TABLE "payment_methods" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "payment_type" varchar NOT NULL, "provider" varchar NOT NULL, "token" varchar NOT NULL, "last_four" varchar NOT NULL, "cardholder_name" varchar, "expires_at" date, "billing_address_line1" varchar, "billing_address_line2" varchar, "billing_city" varchar, "billing_state" varchar, "billing_postal_code" varchar, "billing_country" varchar DEFAULT 'TR', "is_default" boolean DEFAULT 0, "active" boolean DEFAULT 1, "verified_at" datetime(6), "metadata" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_active" ON "payment_methods" ("active")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_is_default" ON "payment_methods" ("is_default")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_payment_type" ON "payment_methods" ("payment_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_provider" ON "payment_methods" ("provider")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_user_id_and_is_default" ON "payment_methods" ("user_id", "is_default")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_user_id" ON "payment_methods" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "payments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "payments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order_id" integer NOT NULL, "payment_method_id" integer NOT NULL, "amount" decimal(10,2) NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "status" integer DEFAULT 0 NOT NULL, "transaction_id" varchar, "gateway_transaction_id" varchar, "payment_gateway" varchar, "processed_at" datetime(6), "failed_at" datetime(6), "refunded_at" datetime(6), "refunded_amount" decimal(10,2) DEFAULT 0.0, "failure_reason" text, "gateway_response" text, "gateway_metadata" json, "receipt_url" varchar, "is_test" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_gateway_transaction_id" ON "payments" ("gateway_transaction_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_order_id_and_status" ON "payments" ("order_id", "status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_order_id" ON "payments" ("order_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_payment_gateway" ON "payments" ("payment_gateway")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_payment_method_id" ON "payments" ("payment_method_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_payments_on_processed_at" ON "payments" ("processed_at")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_payments_on_status" ON "payments" ("status")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_payments_on_transaction_id" ON "payments" ("transaction_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "pregnancy_trackers"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "pregnancy_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "conception_date" date NOT NULL, "due_date" date NOT NULL, "last_menstrual_period" date, "current_week" integer DEFAULT 0, "current_trimester" integer DEFAULT 1, "pre_pregnancy_weight" decimal(5,2), "current_weight" decimal(5,2), "weight_gain" decimal(5,2) DEFAULT 0.0, "fundal_height" decimal(4,1), "fetal_heart_rate" integer, "blood_pressure_systolic" integer, "blood_pressure_diastolic" integer, "glucose_level" decimal(5,2), "iron_level" decimal(5,2), "symptoms" json, "risk_factors" json, "notes" text, "active" boolean DEFAULT 1, "pregnancy_type" varchar DEFAULT 'singleton', "delivery_date" datetime(6), "delivery_type" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_active" ON "pregnancy_trackers" ("active")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_current_trimester" ON "pregnancy_trackers" ("current_trimester")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_current_week" ON "pregnancy_trackers" ("current_week")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_due_date" ON "pregnancy_trackers" ("due_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_patient_id_and_active" ON "pregnancy_trackers" ("patient_id", "active")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_patient_id" ON "pregnancy_trackers" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "prescriptions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "prescriptions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "consultation_id" integer NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "prescription_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "instructions" text, "diagnosis" text, "additional_notes" text, "medications" json, "is_digital" boolean DEFAULT 1, "pharmacy_name" varchar, "pharmacy_address" varchar, "dispensed_at" datetime(6), "dispensed_by" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_consultation_id" ON "prescriptions" ("consultation_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_doctor_id_and_issued_at" ON "prescriptions" ("doctor_id", "issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_doctor_id" ON "prescriptions" ("doctor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_expires_at" ON "prescriptions" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_issued_at" ON "prescriptions" ("issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_patient_id_and_issued_at" ON "prescriptions" ("patient_id", "issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_patient_id" ON "prescriptions" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_prescriptions_on_prescription_number" ON "prescriptions" ("prescription_number")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_status" ON "prescriptions" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "risk_assessments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "risk_assessments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "assessed_by_id" integer, "assessment_type" varchar NOT NULL, "risk_level" varchar NOT NULL, "risk_score" decimal(5,2) NOT NULL, "risk_factors" json, "protective_factors" json, "clinical_notes" text, "recommendations" json, "assessed_at" datetime(6) NOT NULL, "valid_until" datetime(6), "requires_follow_up" boolean DEFAULT 0, "next_assessment_due" datetime(6), "assessment_method" varchar, "assessment_data" json, "status" integer DEFAULT 0, "superseded_by_id" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessed_at" ON "risk_assessments" ("assessed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessed_by_id" ON "risk_assessments" ("assessed_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessment_type" ON "risk_assessments" ("assessment_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_next_assessment_due" ON "risk_assessments" ("next_assessment_due")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "idx_on_patient_id_assessment_type_assessed_at_c1a3b8d791" ON "risk_assessments" ("patient_id", "assessment_type", "assessed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_patient_id" ON "risk_assessments" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_requires_follow_up" ON "risk_assessments" ("requires_follow_up")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_risk_level" ON "risk_assessments" ("risk_level")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_superseded_by_id" ON "risk_assessments" ("superseded_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_valid_until" ON "risk_assessments" ("valid_until")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "session_participants"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "session_participants" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "group_session_id" integer NOT NULL, "user_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "registered_at" datetime(6) NOT NULL, "joined_at" datetime(6), "left_at" datetime(6), "total_duration_seconds" integer DEFAULT 0, "attended" boolean DEFAULT 0, "attendance_percentage" decimal(5,2) DEFAULT 0.0, "feedback" text, "rating" decimal(3,2), "feedback_submitted_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_attended" ON "session_participants" ("attended")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_session_participants_on_group_session_id_and_user_id" ON "session_participants" ("group_session_id", "user_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_group_session_id" ON "session_participants" ("group_session_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_registered_at" ON "session_participants" ("registered_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_status" ON "session_participants" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_user_id" ON "session_participants" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "tier_features"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "tier_features" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "membership_tier_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "limit_value" integer, "is_unlimited" boolean DEFAULT 0, "is_included" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_is_included" ON "tier_features" ("is_included")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_is_unlimited" ON "tier_features" ("is_unlimited")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_membership_feature_id" ON "tier_features" ("membership_feature_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_tier_features_on_tier_and_feature" ON "tier_features" ("membership_tier_id", "membership_feature_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_membership_tier_id" ON "tier_features" ("membership_tier_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "usage_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "usage_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "period_start" date NOT NULL, "period_end" date NOT NULL, "usage_count" integer DEFAULT 0, "limit_exceeded" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_limit_exceeded" ON "usage_trackers" ("limit_exceeded")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_membership_feature_id" ON "usage_trackers" ("membership_feature_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_period_start" ON "usage_trackers" ("period_start")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_usage_trackers_unique" ON "usage_trackers" ("user_id", "membership_feature_id", "period_start")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_user_id" ON "usage_trackers" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "user_memberships"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "user_memberships" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_tier_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "starts_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "auto_renewal" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_expires_at" ON "user_memberships" ("expires_at")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_membership_tier_id" ON "user_memberships" ("membership_tier_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_starts_at" ON "user_memberships" ("starts_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_status" ON "user_memberships" ("status")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_user_id_and_status" ON "user_memberships" ("user_id", "status")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_user_id" ON "user_memberships" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "users"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "users" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "email" varchar NOT NULL, "password_digest" varchar NOT NULL, "first_name" varchar NOT NULL, "last_name" varchar NOT NULL, "phone_number" varchar NOT NULL, "birth_date" date NOT NULL, "verified_at" datetime(6), "locale" varchar DEFAULT 'tr', "timezone" varchar DEFAULT 'Europe/Istanbul', "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_users_on_phone_number" ON "users" ("phone_number")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_users_on_verified_at" ON "users" ("verified_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE IF EXISTS "video_progresses"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "video_progresses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_video_id" integer NOT NULL, "patient_id" integer NOT NULL, "watched_seconds" integer DEFAULT 0 NOT NULL, "progress_percentage" decimal(5,2) DEFAULT 0.0, "completed" boolean DEFAULT 0, "first_watched_at" datetime(6), "last_watched_at" datetime(6), "completed_at" datetime(6), "watch_count" integer DEFAULT 0, "watch_sessions" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_completed" ON "video_progresses" ("completed")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_video_progresses_on_course_video_id_and_patient_id" ON "video_progresses" ("course_video_id", "patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_course_video_id" ON "video_progresses" ("course_video_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_last_watched_at" ON "video_progresses" ("last_watched_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_patient_id" ON "video_progresses" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_progress_percentage" ON "video_progresses" ("progress_percentage")[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aadmins" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "role" varchar DEFAULT 'admin' NOT NULL, "permissions" text, "department" varchar NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aadmins_on_user_id" ON "aadmins" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aadmins_on_role" ON "aadmins" ("role")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aadmins" ("id","user_id","role","permissions","department","created_at","updated_at")
                     SELECT "id","user_id","role","permissions","department","created_at","updated_at" FROM "admins"[0m
  [1m[35m (0.9ms)[0m  [1m[35mDROP TABLE "admins"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "admins" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "role" varchar DEFAULT 'admin' NOT NULL, "permissions" text, "department" varchar NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_378b9734e4"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_admins_on_role" ON "admins" ("role")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_admins_on_user_id" ON "admins" ("user_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "admins" ("id","user_id","role","permissions","department","created_at","updated_at")
                     SELECT "id","user_id","role","permissions","department","created_at","updated_at" FROM "aadmins"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aadmins"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aai_health_insights" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "insight_type" varchar NOT NULL, "title" varchar NOT NULL, "content" text NOT NULL, "summary" text, "confidence_score" decimal(5,4) NOT NULL, "generated_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "priority" integer DEFAULT 1, "data_sources" json, "recommendations" json, "metadata" json, "ai_model_version" varchar, "reviewed_by_doctor" boolean DEFAULT 0, "reviewed_by_id" integer, "reviewed_at" datetime(6), "doctor_notes" text, "patient_acknowledged" boolean DEFAULT 0, "acknowledged_at" datetime(6), "expires_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_status" ON "aai_health_insights" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_reviewed_by_id" ON "aai_health_insights" ("reviewed_by_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_reviewed_by_doctor" ON "aai_health_insights" ("reviewed_by_doctor")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_priority" ON "aai_health_insights" ("priority")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_patient_id" ON "aai_health_insights" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_patient_id_and_generated_at" ON "aai_health_insights" ("patient_id", "generated_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_insight_type" ON "aai_health_insights" ("insight_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_generated_at" ON "aai_health_insights" ("generated_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_confidence_score" ON "aai_health_insights" ("confidence_score")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aai_health_insights" ("id","patient_id","insight_type","title","content","summary","confidence_score","generated_at","status","priority","data_sources","recommendations","metadata","ai_model_version","reviewed_by_doctor","reviewed_by_id","reviewed_at","doctor_notes","patient_acknowledged","acknowledged_at","expires_at","created_at","updated_at")
                     SELECT "id","patient_id","insight_type","title","content","summary","confidence_score","generated_at","status","priority","data_sources","recommendations","metadata","ai_model_version","reviewed_by_doctor","reviewed_by_id","reviewed_at","doctor_notes","patient_acknowledged","acknowledged_at","expires_at","created_at","updated_at" FROM "ai_health_insights"[0m
  [1m[35m (0.5ms)[0m  [1m[35mDROP TABLE "ai_health_insights"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "ai_health_insights" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "insight_type" varchar NOT NULL, "title" varchar NOT NULL, "content" text NOT NULL, "summary" text, "confidence_score" decimal(5,4) NOT NULL, "generated_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "priority" integer DEFAULT 1, "data_sources" json, "recommendations" json, "metadata" json, "ai_model_version" varchar, "reviewed_by_doctor" boolean DEFAULT 0, "reviewed_by_id" integer, "reviewed_at" datetime(6), "doctor_notes" text, "patient_acknowledged" boolean DEFAULT 0, "acknowledged_at" datetime(6), "expires_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_3fd0652fab"
FOREIGN KEY ("reviewed_by_id")
  REFERENCES "doctors" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_confidence_score" ON "ai_health_insights" ("confidence_score")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_generated_at" ON "ai_health_insights" ("generated_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_insight_type" ON "ai_health_insights" ("insight_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_patient_id_and_generated_at" ON "ai_health_insights" ("patient_id", "generated_at")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_patient_id" ON "ai_health_insights" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_priority" ON "ai_health_insights" ("priority")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_reviewed_by_doctor" ON "ai_health_insights" ("reviewed_by_doctor")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_reviewed_by_id" ON "ai_health_insights" ("reviewed_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_status" ON "ai_health_insights" ("status")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "ai_health_insights" ("id","patient_id","insight_type","title","content","summary","confidence_score","generated_at","status","priority","data_sources","recommendations","metadata","ai_model_version","reviewed_by_doctor","reviewed_by_id","reviewed_at","doctor_notes","patient_acknowledged","acknowledged_at","expires_at","created_at","updated_at")
                     SELECT "id","patient_id","insight_type","title","content","summary","confidence_score","generated_at","status","priority","data_sources","recommendations","metadata","ai_model_version","reviewed_by_doctor","reviewed_by_id","reviewed_at","doctor_notes","patient_acknowledged","acknowledged_at","expires_at","created_at","updated_at" FROM "aai_health_insights"[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE "aai_health_insights"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aai_health_insights" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "insight_type" varchar NOT NULL, "title" varchar NOT NULL, "content" text NOT NULL, "summary" text, "confidence_score" decimal(5,4) NOT NULL, "generated_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "priority" integer DEFAULT 1, "data_sources" json, "recommendations" json, "metadata" json, "ai_model_version" varchar, "reviewed_by_doctor" boolean DEFAULT 0, "reviewed_by_id" integer, "reviewed_at" datetime(6), "doctor_notes" text, "patient_acknowledged" boolean DEFAULT 0, "acknowledged_at" datetime(6), "expires_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_status" ON "aai_health_insights" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_reviewed_by_id" ON "aai_health_insights" ("reviewed_by_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_reviewed_by_doctor" ON "aai_health_insights" ("reviewed_by_doctor")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_priority" ON "aai_health_insights" ("priority")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_patient_id" ON "aai_health_insights" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_patient_id_and_generated_at" ON "aai_health_insights" ("patient_id", "generated_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_insight_type" ON "aai_health_insights" ("insight_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_generated_at" ON "aai_health_insights" ("generated_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aai_health_insights_on_confidence_score" ON "aai_health_insights" ("confidence_score")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aai_health_insights" ("id","patient_id","insight_type","title","content","summary","confidence_score","generated_at","status","priority","data_sources","recommendations","metadata","ai_model_version","reviewed_by_doctor","reviewed_by_id","reviewed_at","doctor_notes","patient_acknowledged","acknowledged_at","expires_at","created_at","updated_at")
                     SELECT "id","patient_id","insight_type","title","content","summary","confidence_score","generated_at","status","priority","data_sources","recommendations","metadata","ai_model_version","reviewed_by_doctor","reviewed_by_id","reviewed_at","doctor_notes","patient_acknowledged","acknowledged_at","expires_at","created_at","updated_at" FROM "ai_health_insights"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "ai_health_insights"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "ai_health_insights" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "insight_type" varchar NOT NULL, "title" varchar NOT NULL, "content" text NOT NULL, "summary" text, "confidence_score" decimal(5,4) NOT NULL, "generated_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "priority" integer DEFAULT 1, "data_sources" json, "recommendations" json, "metadata" json, "ai_model_version" varchar, "reviewed_by_doctor" boolean DEFAULT 0, "reviewed_by_id" integer, "reviewed_at" datetime(6), "doctor_notes" text, "patient_acknowledged" boolean DEFAULT 0, "acknowledged_at" datetime(6), "expires_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_3fd0652fab"
FOREIGN KEY ("reviewed_by_id")
  REFERENCES "doctors" ("id")
, CONSTRAINT "fk_rails_f47084857d"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_confidence_score" ON "ai_health_insights" ("confidence_score")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_generated_at" ON "ai_health_insights" ("generated_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_insight_type" ON "ai_health_insights" ("insight_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_patient_id_and_generated_at" ON "ai_health_insights" ("patient_id", "generated_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_patient_id" ON "ai_health_insights" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_priority" ON "ai_health_insights" ("priority")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_reviewed_by_doctor" ON "ai_health_insights" ("reviewed_by_doctor")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_reviewed_by_id" ON "ai_health_insights" ("reviewed_by_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_ai_health_insights_on_status" ON "ai_health_insights" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "ai_health_insights" ("id","patient_id","insight_type","title","content","summary","confidence_score","generated_at","status","priority","data_sources","recommendations","metadata","ai_model_version","reviewed_by_doctor","reviewed_by_id","reviewed_at","doctor_notes","patient_acknowledged","acknowledged_at","expires_at","created_at","updated_at")
                     SELECT "id","patient_id","insight_type","title","content","summary","confidence_score","generated_at","status","priority","data_sources","recommendations","metadata","ai_model_version","reviewed_by_doctor","reviewed_by_id","reviewed_at","doctor_notes","patient_acknowledged","acknowledged_at","expires_at","created_at","updated_at" FROM "aai_health_insights"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aai_health_insights"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aappointments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "appointment_type" varchar NOT NULL, "scheduled_at" datetime(6) NOT NULL, "ends_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "duration_minutes" integer DEFAULT 30 NOT NULL, "consultation_fee" decimal(8,2) NOT NULL, "patient_notes" text, "doctor_notes" text, "meeting_link" varchar, "meeting_id" varchar, "started_at" datetime(6), "ended_at" datetime(6), "cancellation_reason" varchar, "cancelled_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_status" ON "aappointments" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_scheduled_at" ON "aappointments" ("scheduled_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_patient_id" ON "aappointments" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_patient_id_and_scheduled_at" ON "aappointments" ("patient_id", "scheduled_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_doctor_id" ON "aappointments" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_doctor_id_and_scheduled_at" ON "aappointments" ("doctor_id", "scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_appointment_type" ON "aappointments" ("appointment_type")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "aappointments" ("id","patient_id","doctor_id","appointment_type","scheduled_at","ends_at","status","duration_minutes","consultation_fee","patient_notes","doctor_notes","meeting_link","meeting_id","started_at","ended_at","cancellation_reason","cancelled_at","created_at","updated_at")
                     SELECT "id","patient_id","doctor_id","appointment_type","scheduled_at","ends_at","status","duration_minutes","consultation_fee","patient_notes","doctor_notes","meeting_link","meeting_id","started_at","ended_at","cancellation_reason","cancelled_at","created_at","updated_at" FROM "appointments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "appointments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "appointments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "appointment_type" varchar NOT NULL, "scheduled_at" datetime(6) NOT NULL, "ends_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "duration_minutes" integer DEFAULT 30 NOT NULL, "consultation_fee" decimal(8,2) NOT NULL, "patient_notes" text, "doctor_notes" text, "meeting_link" varchar, "meeting_id" varchar, "started_at" datetime(6), "ended_at" datetime(6), "cancellation_reason" varchar, "cancelled_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_8db8e1e8a5"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_appointment_type" ON "appointments" ("appointment_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_doctor_id_and_scheduled_at" ON "appointments" ("doctor_id", "scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_doctor_id" ON "appointments" ("doctor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_patient_id_and_scheduled_at" ON "appointments" ("patient_id", "scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_patient_id" ON "appointments" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_scheduled_at" ON "appointments" ("scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_status" ON "appointments" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "appointments" ("id","patient_id","doctor_id","appointment_type","scheduled_at","ends_at","status","duration_minutes","consultation_fee","patient_notes","doctor_notes","meeting_link","meeting_id","started_at","ended_at","cancellation_reason","cancelled_at","created_at","updated_at")
                     SELECT "id","patient_id","doctor_id","appointment_type","scheduled_at","ends_at","status","duration_minutes","consultation_fee","patient_notes","doctor_notes","meeting_link","meeting_id","started_at","ended_at","cancellation_reason","cancelled_at","created_at","updated_at" FROM "aappointments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aappointments"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aappointments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "appointment_type" varchar NOT NULL, "scheduled_at" datetime(6) NOT NULL, "ends_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "duration_minutes" integer DEFAULT 30 NOT NULL, "consultation_fee" decimal(8,2) NOT NULL, "patient_notes" text, "doctor_notes" text, "meeting_link" varchar, "meeting_id" varchar, "started_at" datetime(6), "ended_at" datetime(6), "cancellation_reason" varchar, "cancelled_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_status" ON "aappointments" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_scheduled_at" ON "aappointments" ("scheduled_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_patient_id" ON "aappointments" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_patient_id_and_scheduled_at" ON "aappointments" ("patient_id", "scheduled_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_doctor_id" ON "aappointments" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_doctor_id_and_scheduled_at" ON "aappointments" ("doctor_id", "scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aappointments_on_appointment_type" ON "aappointments" ("appointment_type")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aappointments" ("id","patient_id","doctor_id","appointment_type","scheduled_at","ends_at","status","duration_minutes","consultation_fee","patient_notes","doctor_notes","meeting_link","meeting_id","started_at","ended_at","cancellation_reason","cancelled_at","created_at","updated_at")
                     SELECT "id","patient_id","doctor_id","appointment_type","scheduled_at","ends_at","status","duration_minutes","consultation_fee","patient_notes","doctor_notes","meeting_link","meeting_id","started_at","ended_at","cancellation_reason","cancelled_at","created_at","updated_at" FROM "appointments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "appointments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "appointments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "appointment_type" varchar NOT NULL, "scheduled_at" datetime(6) NOT NULL, "ends_at" datetime(6) NOT NULL, "status" integer DEFAULT 0 NOT NULL, "duration_minutes" integer DEFAULT 30 NOT NULL, "consultation_fee" decimal(8,2) NOT NULL, "patient_notes" text, "doctor_notes" text, "meeting_link" varchar, "meeting_id" varchar, "started_at" datetime(6), "ended_at" datetime(6), "cancellation_reason" varchar, "cancelled_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_8db8e1e8a5"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
, CONSTRAINT "fk_rails_c63da04ab4"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_appointment_type" ON "appointments" ("appointment_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_doctor_id_and_scheduled_at" ON "appointments" ("doctor_id", "scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_doctor_id" ON "appointments" ("doctor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_patient_id_and_scheduled_at" ON "appointments" ("patient_id", "scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_patient_id" ON "appointments" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_scheduled_at" ON "appointments" ("scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_appointments_on_status" ON "appointments" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "appointments" ("id","patient_id","doctor_id","appointment_type","scheduled_at","ends_at","status","duration_minutes","consultation_fee","patient_notes","doctor_notes","meeting_link","meeting_id","started_at","ended_at","cancellation_reason","cancelled_at","created_at","updated_at")
                     SELECT "id","patient_id","doctor_id","appointment_type","scheduled_at","ends_at","status","duration_minutes","consultation_fee","patient_notes","doctor_notes","meeting_link","meeting_id","started_at","ended_at","cancellation_reason","cancelled_at","created_at","updated_at" FROM "aappointments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aappointments"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aattachments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "attachable_type" varchar NOT NULL, "attachable_id" integer NOT NULL, "user_id" integer NOT NULL, "file_name" varchar NOT NULL, "original_file_name" varchar NOT NULL, "file_url" varchar NOT NULL, "file_type" varchar NOT NULL, "file_size_bytes" bigint NOT NULL, "file_hash" varchar, "mime_type" varchar, "description" text, "is_public" boolean DEFAULT 0, "is_processed" boolean DEFAULT 0, "processing_metadata" json, "thumbnail_url" varchar, "uploaded_at" datetime(6) NOT NULL, "last_accessed_at" datetime(6), "download_count" integer DEFAULT 0, "expires_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aattachments_on_user_id" ON "aattachments" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aattachments_on_uploaded_at" ON "aattachments" ("uploaded_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aattachments_on_is_public" ON "aattachments" ("is_public")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aattachments_on_file_type" ON "aattachments" ("file_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_aattachments_on_file_hash" ON "aattachments" ("file_hash")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aattachments_on_attachable_type_and_attachable_id" ON "aattachments" ("attachable_type", "attachable_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aattachments_on_attachable" ON "aattachments" ("attachable_type", "attachable_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aattachments" ("id","attachable_type","attachable_id","user_id","file_name","original_file_name","file_url","file_type","file_size_bytes","file_hash","mime_type","description","is_public","is_processed","processing_metadata","thumbnail_url","uploaded_at","last_accessed_at","download_count","expires_at","created_at","updated_at")
                     SELECT "id","attachable_type","attachable_id","user_id","file_name","original_file_name","file_url","file_type","file_size_bytes","file_hash","mime_type","description","is_public","is_processed","processing_metadata","thumbnail_url","uploaded_at","last_accessed_at","download_count","expires_at","created_at","updated_at" FROM "attachments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "attachments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "attachments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "attachable_type" varchar NOT NULL, "attachable_id" integer NOT NULL, "user_id" integer NOT NULL, "file_name" varchar NOT NULL, "original_file_name" varchar NOT NULL, "file_url" varchar NOT NULL, "file_type" varchar NOT NULL, "file_size_bytes" bigint NOT NULL, "file_hash" varchar, "mime_type" varchar, "description" text, "is_public" boolean DEFAULT 0, "is_processed" boolean DEFAULT 0, "processing_metadata" json, "thumbnail_url" varchar, "uploaded_at" datetime(6) NOT NULL, "last_accessed_at" datetime(6), "download_count" integer DEFAULT 0, "expires_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_5650a5e7db"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_attachable" ON "attachments" ("attachable_type", "attachable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_attachable_type_and_attachable_id" ON "attachments" ("attachable_type", "attachable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_attachments_on_file_hash" ON "attachments" ("file_hash")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_file_type" ON "attachments" ("file_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_is_public" ON "attachments" ("is_public")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_uploaded_at" ON "attachments" ("uploaded_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_attachments_on_user_id" ON "attachments" ("user_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "attachments" ("id","attachable_type","attachable_id","user_id","file_name","original_file_name","file_url","file_type","file_size_bytes","file_hash","mime_type","description","is_public","is_processed","processing_metadata","thumbnail_url","uploaded_at","last_accessed_at","download_count","expires_at","created_at","updated_at")
                     SELECT "id","attachable_type","attachable_id","user_id","file_name","original_file_name","file_url","file_type","file_size_bytes","file_hash","mime_type","description","is_public","is_processed","processing_metadata","thumbnail_url","uploaded_at","last_accessed_at","download_count","expires_at","created_at","updated_at" FROM "aattachments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aattachments"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "acalendar_integrations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "provider" varchar NOT NULL, "provider_user_id" varchar, "access_token" varchar NOT NULL, "refresh_token" varchar, "calendar_id" varchar, "calendar_name" varchar, "token_expires_at" datetime(6), "active" boolean DEFAULT 1, "sync_appointments" boolean DEFAULT 1, "sync_reminders" boolean DEFAULT 1, "last_sync_at" datetime(6), "sync_settings" json, "provider_metadata" json, "sync_errors" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acalendar_integrations_on_user_id" ON "acalendar_integrations" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_acalendar_integrations_on_user_id_and_provider" ON "acalendar_integrations" ("user_id", "provider")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acalendar_integrations_on_provider" ON "acalendar_integrations" ("provider")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acalendar_integrations_on_last_sync_at" ON "acalendar_integrations" ("last_sync_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acalendar_integrations_on_active" ON "acalendar_integrations" ("active")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "acalendar_integrations" ("id","user_id","provider","provider_user_id","access_token","refresh_token","calendar_id","calendar_name","token_expires_at","active","sync_appointments","sync_reminders","last_sync_at","sync_settings","provider_metadata","sync_errors","created_at","updated_at")
                     SELECT "id","user_id","provider","provider_user_id","access_token","refresh_token","calendar_id","calendar_name","token_expires_at","active","sync_appointments","sync_reminders","last_sync_at","sync_settings","provider_metadata","sync_errors","created_at","updated_at" FROM "calendar_integrations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "calendar_integrations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "calendar_integrations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "provider" varchar NOT NULL, "provider_user_id" varchar, "access_token" varchar NOT NULL, "refresh_token" varchar, "calendar_id" varchar, "calendar_name" varchar, "token_expires_at" datetime(6), "active" boolean DEFAULT 1, "sync_appointments" boolean DEFAULT 1, "sync_reminders" boolean DEFAULT 1, "last_sync_at" datetime(6), "sync_settings" json, "provider_metadata" json, "sync_errors" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_03c9b4745a"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_active" ON "calendar_integrations" ("active")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_last_sync_at" ON "calendar_integrations" ("last_sync_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_provider" ON "calendar_integrations" ("provider")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_calendar_integrations_on_user_id_and_provider" ON "calendar_integrations" ("user_id", "provider")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_calendar_integrations_on_user_id" ON "calendar_integrations" ("user_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "calendar_integrations" ("id","user_id","provider","provider_user_id","access_token","refresh_token","calendar_id","calendar_name","token_expires_at","active","sync_appointments","sync_reminders","last_sync_at","sync_settings","provider_metadata","sync_errors","created_at","updated_at")
                     SELECT "id","user_id","provider","provider_user_id","access_token","refresh_token","calendar_id","calendar_name","token_expires_at","active","sync_appointments","sync_reminders","last_sync_at","sync_settings","provider_metadata","sync_errors","created_at","updated_at" FROM "acalendar_integrations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "acalendar_integrations"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aconsultations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "appointment_id" integer, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "consultation_type" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "started_at" datetime(6), "ended_at" datetime(6), "duration_minutes" integer, "chief_complaint" text NOT NULL, "history_of_present_illness" text, "physical_examination" text, "assessment" text, "plan" text, "follow_up_instructions" text, "additional_notes" text, "consultation_fee" decimal(8,2), "consultation_method" varchar, "vital_signs" json, "symptoms" json, "prescription_issued" boolean DEFAULT 0, "next_follow_up_date" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_status" ON "aconsultations" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_started_at" ON "aconsultations" ("started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_patient_id" ON "aconsultations" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_patient_id_and_started_at" ON "aconsultations" ("patient_id", "started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_next_follow_up_date" ON "aconsultations" ("next_follow_up_date")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_doctor_id" ON "aconsultations" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_doctor_id_and_started_at" ON "aconsultations" ("doctor_id", "started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_consultation_type" ON "aconsultations" ("consultation_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_appointment_id" ON "aconsultations" ("appointment_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aconsultations" ("id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at")
                     SELECT "id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at" FROM "consultations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "consultations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "consultations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "appointment_id" integer, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "consultation_type" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "started_at" datetime(6), "ended_at" datetime(6), "duration_minutes" integer, "chief_complaint" text NOT NULL, "history_of_present_illness" text, "physical_examination" text, "assessment" text, "plan" text, "follow_up_instructions" text, "additional_notes" text, "consultation_fee" decimal(8,2), "consultation_method" varchar, "vital_signs" json, "symptoms" json, "prescription_issued" boolean DEFAULT 0, "next_follow_up_date" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_04612d29f0"
FOREIGN KEY ("appointment_id")
  REFERENCES "appointments" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_appointment_id" ON "consultations" ("appointment_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_consultation_type" ON "consultations" ("consultation_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_doctor_id_and_started_at" ON "consultations" ("doctor_id", "started_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_doctor_id" ON "consultations" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_next_follow_up_date" ON "consultations" ("next_follow_up_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_patient_id_and_started_at" ON "consultations" ("patient_id", "started_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_patient_id" ON "consultations" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_started_at" ON "consultations" ("started_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_status" ON "consultations" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "consultations" ("id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at")
                     SELECT "id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at" FROM "aconsultations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aconsultations"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aconsultations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "appointment_id" integer, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "consultation_type" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "started_at" datetime(6), "ended_at" datetime(6), "duration_minutes" integer, "chief_complaint" text NOT NULL, "history_of_present_illness" text, "physical_examination" text, "assessment" text, "plan" text, "follow_up_instructions" text, "additional_notes" text, "consultation_fee" decimal(8,2), "consultation_method" varchar, "vital_signs" json, "symptoms" json, "prescription_issued" boolean DEFAULT 0, "next_follow_up_date" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_status" ON "aconsultations" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_started_at" ON "aconsultations" ("started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_patient_id" ON "aconsultations" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_patient_id_and_started_at" ON "aconsultations" ("patient_id", "started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_next_follow_up_date" ON "aconsultations" ("next_follow_up_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_doctor_id" ON "aconsultations" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_doctor_id_and_started_at" ON "aconsultations" ("doctor_id", "started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_consultation_type" ON "aconsultations" ("consultation_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_appointment_id" ON "aconsultations" ("appointment_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aconsultations" ("id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at")
                     SELECT "id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at" FROM "consultations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "consultations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "consultations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "appointment_id" integer, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "consultation_type" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "started_at" datetime(6), "ended_at" datetime(6), "duration_minutes" integer, "chief_complaint" text NOT NULL, "history_of_present_illness" text, "physical_examination" text, "assessment" text, "plan" text, "follow_up_instructions" text, "additional_notes" text, "consultation_fee" decimal(8,2), "consultation_method" varchar, "vital_signs" json, "symptoms" json, "prescription_issued" boolean DEFAULT 0, "next_follow_up_date" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_04612d29f0"
FOREIGN KEY ("appointment_id")
  REFERENCES "appointments" ("id")
, CONSTRAINT "fk_rails_b1f629cdac"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_appointment_id" ON "consultations" ("appointment_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_consultation_type" ON "consultations" ("consultation_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_doctor_id_and_started_at" ON "consultations" ("doctor_id", "started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_doctor_id" ON "consultations" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_next_follow_up_date" ON "consultations" ("next_follow_up_date")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_patient_id_and_started_at" ON "consultations" ("patient_id", "started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_patient_id" ON "consultations" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_started_at" ON "consultations" ("started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_status" ON "consultations" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "consultations" ("id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at")
                     SELECT "id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at" FROM "aconsultations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aconsultations"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aconsultations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "appointment_id" integer, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "consultation_type" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "started_at" datetime(6), "ended_at" datetime(6), "duration_minutes" integer, "chief_complaint" text NOT NULL, "history_of_present_illness" text, "physical_examination" text, "assessment" text, "plan" text, "follow_up_instructions" text, "additional_notes" text, "consultation_fee" decimal(8,2), "consultation_method" varchar, "vital_signs" json, "symptoms" json, "prescription_issued" boolean DEFAULT 0, "next_follow_up_date" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_status" ON "aconsultations" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_started_at" ON "aconsultations" ("started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_patient_id" ON "aconsultations" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_patient_id_and_started_at" ON "aconsultations" ("patient_id", "started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_next_follow_up_date" ON "aconsultations" ("next_follow_up_date")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_doctor_id" ON "aconsultations" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_doctor_id_and_started_at" ON "aconsultations" ("doctor_id", "started_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_consultation_type" ON "aconsultations" ("consultation_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aconsultations_on_appointment_id" ON "aconsultations" ("appointment_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aconsultations" ("id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at")
                     SELECT "id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at" FROM "consultations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "consultations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "consultations" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "appointment_id" integer, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "consultation_type" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "started_at" datetime(6), "ended_at" datetime(6), "duration_minutes" integer, "chief_complaint" text NOT NULL, "history_of_present_illness" text, "physical_examination" text, "assessment" text, "plan" text, "follow_up_instructions" text, "additional_notes" text, "consultation_fee" decimal(8,2), "consultation_method" varchar, "vital_signs" json, "symptoms" json, "prescription_issued" boolean DEFAULT 0, "next_follow_up_date" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_b1f629cdac"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
, CONSTRAINT "fk_rails_04612d29f0"
FOREIGN KEY ("appointment_id")
  REFERENCES "appointments" ("id")
, CONSTRAINT "fk_rails_33c52f1c05"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_appointment_id" ON "consultations" ("appointment_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_consultation_type" ON "consultations" ("consultation_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_doctor_id_and_started_at" ON "consultations" ("doctor_id", "started_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_doctor_id" ON "consultations" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_next_follow_up_date" ON "consultations" ("next_follow_up_date")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_patient_id_and_started_at" ON "consultations" ("patient_id", "started_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_patient_id" ON "consultations" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_started_at" ON "consultations" ("started_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_consultations_on_status" ON "consultations" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "consultations" ("id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at")
                     SELECT "id","appointment_id","patient_id","doctor_id","consultation_type","status","started_at","ended_at","duration_minutes","chief_complaint","history_of_present_illness","physical_examination","assessment","plan","follow_up_instructions","additional_notes","consultation_fee","consultation_method","vital_signs","symptoms","prescription_issued","next_follow_up_date","created_at","updated_at" FROM "aconsultations"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aconsultations"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "acourse_enrollments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_id" integer NOT NULL, "patient_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "enrolled_at" datetime(6) NOT NULL, "completed_at" datetime(6), "last_accessed_at" datetime(6), "progress_percentage" decimal(5,2) DEFAULT 0.0, "total_watch_time_seconds" integer DEFAULT 0, "rating" decimal(3,2), "review" text, "review_submitted_at" datetime(6), "certificate_issued" boolean DEFAULT 0, "certificate_issued_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_status" ON "acourse_enrollments" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_progress_percentage" ON "acourse_enrollments" ("progress_percentage")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_patient_id" ON "acourse_enrollments" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_enrolled_at" ON "acourse_enrollments" ("enrolled_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_course_id" ON "acourse_enrollments" ("course_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_acourse_enrollments_on_course_id_and_patient_id" ON "acourse_enrollments" ("course_id", "patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_completed_at" ON "acourse_enrollments" ("completed_at")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "acourse_enrollments" ("id","course_id","patient_id","status","enrolled_at","completed_at","last_accessed_at","progress_percentage","total_watch_time_seconds","rating","review","review_submitted_at","certificate_issued","certificate_issued_at","created_at","updated_at")
                     SELECT "id","course_id","patient_id","status","enrolled_at","completed_at","last_accessed_at","progress_percentage","total_watch_time_seconds","rating","review","review_submitted_at","certificate_issued","certificate_issued_at","created_at","updated_at" FROM "course_enrollments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "course_enrollments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "course_enrollments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_id" integer NOT NULL, "patient_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "enrolled_at" datetime(6) NOT NULL, "completed_at" datetime(6), "last_accessed_at" datetime(6), "progress_percentage" decimal(5,2) DEFAULT 0.0, "total_watch_time_seconds" integer DEFAULT 0, "rating" decimal(3,2), "review" text, "review_submitted_at" datetime(6), "certificate_issued" boolean DEFAULT 0, "certificate_issued_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_875ed9c80f"
FOREIGN KEY ("course_id")
  REFERENCES "courses" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_completed_at" ON "course_enrollments" ("completed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_course_enrollments_on_course_id_and_patient_id" ON "course_enrollments" ("course_id", "patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_course_id" ON "course_enrollments" ("course_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_enrolled_at" ON "course_enrollments" ("enrolled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_patient_id" ON "course_enrollments" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_progress_percentage" ON "course_enrollments" ("progress_percentage")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_status" ON "course_enrollments" ("status")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "course_enrollments" ("id","course_id","patient_id","status","enrolled_at","completed_at","last_accessed_at","progress_percentage","total_watch_time_seconds","rating","review","review_submitted_at","certificate_issued","certificate_issued_at","created_at","updated_at")
                     SELECT "id","course_id","patient_id","status","enrolled_at","completed_at","last_accessed_at","progress_percentage","total_watch_time_seconds","rating","review","review_submitted_at","certificate_issued","certificate_issued_at","created_at","updated_at" FROM "acourse_enrollments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "acourse_enrollments"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "acourse_enrollments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_id" integer NOT NULL, "patient_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "enrolled_at" datetime(6) NOT NULL, "completed_at" datetime(6), "last_accessed_at" datetime(6), "progress_percentage" decimal(5,2) DEFAULT 0.0, "total_watch_time_seconds" integer DEFAULT 0, "rating" decimal(3,2), "review" text, "review_submitted_at" datetime(6), "certificate_issued" boolean DEFAULT 0, "certificate_issued_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_status" ON "acourse_enrollments" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_progress_percentage" ON "acourse_enrollments" ("progress_percentage")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_patient_id" ON "acourse_enrollments" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_enrolled_at" ON "acourse_enrollments" ("enrolled_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_course_id" ON "acourse_enrollments" ("course_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_acourse_enrollments_on_course_id_and_patient_id" ON "acourse_enrollments" ("course_id", "patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_enrollments_on_completed_at" ON "acourse_enrollments" ("completed_at")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "acourse_enrollments" ("id","course_id","patient_id","status","enrolled_at","completed_at","last_accessed_at","progress_percentage","total_watch_time_seconds","rating","review","review_submitted_at","certificate_issued","certificate_issued_at","created_at","updated_at")
                     SELECT "id","course_id","patient_id","status","enrolled_at","completed_at","last_accessed_at","progress_percentage","total_watch_time_seconds","rating","review","review_submitted_at","certificate_issued","certificate_issued_at","created_at","updated_at" FROM "course_enrollments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "course_enrollments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "course_enrollments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_id" integer NOT NULL, "patient_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "enrolled_at" datetime(6) NOT NULL, "completed_at" datetime(6), "last_accessed_at" datetime(6), "progress_percentage" decimal(5,2) DEFAULT 0.0, "total_watch_time_seconds" integer DEFAULT 0, "rating" decimal(3,2), "review" text, "review_submitted_at" datetime(6), "certificate_issued" boolean DEFAULT 0, "certificate_issued_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_875ed9c80f"
FOREIGN KEY ("course_id")
  REFERENCES "courses" ("id")
, CONSTRAINT "fk_rails_c6946b5a3d"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_completed_at" ON "course_enrollments" ("completed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_course_enrollments_on_course_id_and_patient_id" ON "course_enrollments" ("course_id", "patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_course_id" ON "course_enrollments" ("course_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_enrolled_at" ON "course_enrollments" ("enrolled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_patient_id" ON "course_enrollments" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_progress_percentage" ON "course_enrollments" ("progress_percentage")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_enrollments_on_status" ON "course_enrollments" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "course_enrollments" ("id","course_id","patient_id","status","enrolled_at","completed_at","last_accessed_at","progress_percentage","total_watch_time_seconds","rating","review","review_submitted_at","certificate_issued","certificate_issued_at","created_at","updated_at")
                     SELECT "id","course_id","patient_id","status","enrolled_at","completed_at","last_accessed_at","progress_percentage","total_watch_time_seconds","rating","review","review_submitted_at","certificate_issued","certificate_issued_at","created_at","updated_at" FROM "acourse_enrollments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "acourse_enrollments"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.2ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "acourse_modules" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_id" integer NOT NULL, "title" varchar NOT NULL, "description" text, "sort_order" integer NOT NULL, "duration_minutes" integer DEFAULT 0, "is_published" boolean DEFAULT 0, "learning_objectives" json, "content_summary" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_modules_on_is_published" ON "acourse_modules" ("is_published")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_modules_on_course_id" ON "acourse_modules" ("course_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_acourse_modules_on_course_id_and_sort_order" ON "acourse_modules" ("course_id", "sort_order")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "acourse_modules" ("id","course_id","title","description","sort_order","duration_minutes","is_published","learning_objectives","content_summary","created_at","updated_at")
                     SELECT "id","course_id","title","description","sort_order","duration_minutes","is_published","learning_objectives","content_summary","created_at","updated_at" FROM "course_modules"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "course_modules"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "course_modules" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_id" integer NOT NULL, "title" varchar NOT NULL, "description" text, "sort_order" integer NOT NULL, "duration_minutes" integer DEFAULT 0, "is_published" boolean DEFAULT 0, "learning_objectives" json, "content_summary" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_74391d7a5f"
FOREIGN KEY ("course_id")
  REFERENCES "courses" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_course_modules_on_course_id_and_sort_order" ON "course_modules" ("course_id", "sort_order")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_course_modules_on_course_id" ON "course_modules" ("course_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_modules_on_is_published" ON "course_modules" ("is_published")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "course_modules" ("id","course_id","title","description","sort_order","duration_minutes","is_published","learning_objectives","content_summary","created_at","updated_at")
                     SELECT "id","course_id","title","description","sort_order","duration_minutes","is_published","learning_objectives","content_summary","created_at","updated_at" FROM "acourse_modules"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "acourse_modules"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "acourse_videos" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_module_id" integer NOT NULL, "title" varchar NOT NULL, "description" text, "video_url" varchar NOT NULL, "thumbnail_url" varchar, "duration_seconds" integer NOT NULL, "sort_order" integer NOT NULL, "is_published" boolean DEFAULT 0, "is_preview" boolean DEFAULT 0, "video_quality" varchar, "file_size_bytes" bigint, "subtitles" json, "video_metadata" json, "view_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_videos_on_is_published" ON "acourse_videos" ("is_published")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_videos_on_is_preview" ON "acourse_videos" ("is_preview")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourse_videos_on_course_module_id" ON "acourse_videos" ("course_module_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_acourse_videos_on_course_module_id_and_sort_order" ON "acourse_videos" ("course_module_id", "sort_order")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "acourse_videos" ("id","course_module_id","title","description","video_url","thumbnail_url","duration_seconds","sort_order","is_published","is_preview","video_quality","file_size_bytes","subtitles","video_metadata","view_count","created_at","updated_at")
                     SELECT "id","course_module_id","title","description","video_url","thumbnail_url","duration_seconds","sort_order","is_published","is_preview","video_quality","file_size_bytes","subtitles","video_metadata","view_count","created_at","updated_at" FROM "course_videos"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "course_videos"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "course_videos" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_module_id" integer NOT NULL, "title" varchar NOT NULL, "description" text, "video_url" varchar NOT NULL, "thumbnail_url" varchar, "duration_seconds" integer NOT NULL, "sort_order" integer NOT NULL, "is_published" boolean DEFAULT 0, "is_preview" boolean DEFAULT 0, "video_quality" varchar, "file_size_bytes" bigint, "subtitles" json, "video_metadata" json, "view_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_3d40bb2a3c"
FOREIGN KEY ("course_module_id")
  REFERENCES "course_modules" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_course_videos_on_course_module_id_and_sort_order" ON "course_videos" ("course_module_id", "sort_order")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_course_videos_on_course_module_id" ON "course_videos" ("course_module_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_course_videos_on_is_preview" ON "course_videos" ("is_preview")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_course_videos_on_is_published" ON "course_videos" ("is_published")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "course_videos" ("id","course_module_id","title","description","video_url","thumbnail_url","duration_seconds","sort_order","is_published","is_preview","video_quality","file_size_bytes","subtitles","video_metadata","view_count","created_at","updated_at")
                     SELECT "id","course_module_id","title","description","video_url","thumbnail_url","duration_seconds","sort_order","is_published","is_preview","video_quality","file_size_bytes","subtitles","video_metadata","view_count","created_at","updated_at" FROM "acourse_videos"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "acourse_videos"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (10.4ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TEMPORARY TABLE "acourses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "instructor_id" integer NOT NULL, "title" varchar NOT NULL, "description" text NOT NULL, "slug" varchar NOT NULL, "category" varchar NOT NULL, "difficulty_level" varchar NOT NULL, "duration_minutes" integer DEFAULT 0, "price" decimal(8,2) DEFAULT 0.0, "currency" varchar DEFAULT 'TRY', "status" integer DEFAULT 0 NOT NULL, "published_at" datetime(6), "thumbnail_url" varchar, "preview_video_url" varchar, "learning_objectives" json, "prerequisites" json, "target_audience" json, "enrollment_count" integer DEFAULT 0, "rating" decimal(3,2), "review_count" integer DEFAULT 0, "is_featured" boolean DEFAULT 0, "sort_order" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_acourses_on_status" ON "acourses" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_acourses_on_slug" ON "acourses" ("slug")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_acourses_on_rating" ON "acourses" ("rating")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_acourses_on_published_at" ON "acourses" ("published_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourses_on_is_featured" ON "acourses" ("is_featured")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourses_on_instructor_id" ON "acourses" ("instructor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourses_on_instructor_id_and_status" ON "acourses" ("instructor_id", "status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourses_on_difficulty_level" ON "acourses" ("difficulty_level")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acourses_on_category" ON "acourses" ("category")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "acourses" ("id","instructor_id","title","description","slug","category","difficulty_level","duration_minutes","price","currency","status","published_at","thumbnail_url","preview_video_url","learning_objectives","prerequisites","target_audience","enrollment_count","rating","review_count","is_featured","sort_order","created_at","updated_at")
                     SELECT "id","instructor_id","title","description","slug","category","difficulty_level","duration_minutes","price","currency","status","published_at","thumbnail_url","preview_video_url","learning_objectives","prerequisites","target_audience","enrollment_count","rating","review_count","is_featured","sort_order","created_at","updated_at" FROM "courses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "courses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "courses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "instructor_id" integer NOT NULL, "title" varchar NOT NULL, "description" text NOT NULL, "slug" varchar NOT NULL, "category" varchar NOT NULL, "difficulty_level" varchar NOT NULL, "duration_minutes" integer DEFAULT 0, "price" decimal(8,2) DEFAULT 0.0, "currency" varchar DEFAULT 'TRY', "status" integer DEFAULT 0 NOT NULL, "published_at" datetime(6), "thumbnail_url" varchar, "preview_video_url" varchar, "learning_objectives" json, "prerequisites" json, "target_audience" json, "enrollment_count" integer DEFAULT 0, "rating" decimal(3,2), "review_count" integer DEFAULT 0, "is_featured" boolean DEFAULT 0, "sort_order" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_2ab3132eb0"
FOREIGN KEY ("instructor_id")
  REFERENCES "doctors" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_category" ON "courses" ("category")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_difficulty_level" ON "courses" ("difficulty_level")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_instructor_id_and_status" ON "courses" ("instructor_id", "status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_instructor_id" ON "courses" ("instructor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_is_featured" ON "courses" ("is_featured")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_published_at" ON "courses" ("published_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_rating" ON "courses" ("rating")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_courses_on_slug" ON "courses" ("slug")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_courses_on_status" ON "courses" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "courses" ("id","instructor_id","title","description","slug","category","difficulty_level","duration_minutes","price","currency","status","published_at","thumbnail_url","preview_video_url","learning_objectives","prerequisites","target_audience","enrollment_count","rating","review_count","is_featured","sort_order","created_at","updated_at")
                     SELECT "id","instructor_id","title","description","slug","category","difficulty_level","duration_minutes","price","currency","status","published_at","thumbnail_url","preview_video_url","learning_objectives","prerequisites","target_audience","enrollment_count","rating","review_count","is_featured","sort_order","created_at","updated_at" FROM "acourses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "acourses"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (1.4ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "acycle_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "cycle_start_date" date NOT NULL, "cycle_end_date" date, "predicted_next_cycle" date, "cycle_length" integer, "period_length" integer, "flow_intensity" varchar, "symptoms" json, "mood_data" json, "notes" text, "is_irregular" boolean DEFAULT 0, "basal_body_temperature" decimal(4,2), "cervical_mucus_type" varchar, "ovulation_detected" boolean DEFAULT 0, "ovulation_date" date, "fertility_signs" json, "contraception_used" boolean DEFAULT 0, "contraception_type" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acycle_trackers_on_predicted_next_cycle" ON "acycle_trackers" ("predicted_next_cycle")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acycle_trackers_on_patient_id" ON "acycle_trackers" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acycle_trackers_on_patient_id_and_cycle_start_date" ON "acycle_trackers" ("patient_id", "cycle_start_date")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acycle_trackers_on_ovulation_date" ON "acycle_trackers" ("ovulation_date")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_acycle_trackers_on_cycle_start_date" ON "acycle_trackers" ("cycle_start_date")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "acycle_trackers" ("id","patient_id","cycle_start_date","cycle_end_date","predicted_next_cycle","cycle_length","period_length","flow_intensity","symptoms","mood_data","notes","is_irregular","basal_body_temperature","cervical_mucus_type","ovulation_detected","ovulation_date","fertility_signs","contraception_used","contraception_type","created_at","updated_at")
                     SELECT "id","patient_id","cycle_start_date","cycle_end_date","predicted_next_cycle","cycle_length","period_length","flow_intensity","symptoms","mood_data","notes","is_irregular","basal_body_temperature","cervical_mucus_type","ovulation_detected","ovulation_date","fertility_signs","contraception_used","contraception_type","created_at","updated_at" FROM "cycle_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "cycle_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "cycle_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "cycle_start_date" date NOT NULL, "cycle_end_date" date, "predicted_next_cycle" date, "cycle_length" integer, "period_length" integer, "flow_intensity" varchar, "symptoms" json, "mood_data" json, "notes" text, "is_irregular" boolean DEFAULT 0, "basal_body_temperature" decimal(4,2), "cervical_mucus_type" varchar, "ovulation_detected" boolean DEFAULT 0, "ovulation_date" date, "fertility_signs" json, "contraception_used" boolean DEFAULT 0, "contraception_type" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_1f5eb00fc5"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_cycle_start_date" ON "cycle_trackers" ("cycle_start_date")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_ovulation_date" ON "cycle_trackers" ("ovulation_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_patient_id_and_cycle_start_date" ON "cycle_trackers" ("patient_id", "cycle_start_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_patient_id" ON "cycle_trackers" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_cycle_trackers_on_predicted_next_cycle" ON "cycle_trackers" ("predicted_next_cycle")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "cycle_trackers" ("id","patient_id","cycle_start_date","cycle_end_date","predicted_next_cycle","cycle_length","period_length","flow_intensity","symptoms","mood_data","notes","is_irregular","basal_body_temperature","cervical_mucus_type","ovulation_detected","ovulation_date","fertility_signs","contraception_used","contraception_type","created_at","updated_at")
                     SELECT "id","patient_id","cycle_start_date","cycle_end_date","predicted_next_cycle","cycle_length","period_length","flow_intensity","symptoms","mood_data","notes","is_irregular","basal_body_temperature","cervical_mucus_type","ovulation_detected","ovulation_date","fertility_signs","contraception_used","contraception_type","created_at","updated_at" FROM "acycle_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "acycle_trackers"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "adoctor_availabilities" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "day_of_week" integer NOT NULL, "start_time" varchar NOT NULL, "end_time" varchar NOT NULL, "appointment_duration" integer DEFAULT 30 NOT NULL, "buffer_time" integer DEFAULT 5, "max_appointments" integer DEFAULT 16 NOT NULL, "is_available" boolean DEFAULT 1, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_adoctor_availabilities_on_is_available" ON "adoctor_availabilities" ("is_available")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_adoctor_availabilities_on_doctor_id" ON "adoctor_availabilities" ("doctor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_adoctor_availabilities_on_doctor_id_and_day_of_week" ON "adoctor_availabilities" ("doctor_id", "day_of_week")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_adoctor_availabilities_on_day_of_week" ON "adoctor_availabilities" ("day_of_week")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "adoctor_availabilities" ("id","doctor_id","day_of_week","start_time","end_time","appointment_duration","buffer_time","max_appointments","is_available","created_at","updated_at")
                     SELECT "id","doctor_id","day_of_week","start_time","end_time","appointment_duration","buffer_time","max_appointments","is_available","created_at","updated_at" FROM "doctor_availabilities"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "doctor_availabilities"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "doctor_availabilities" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "day_of_week" integer NOT NULL, "start_time" varchar NOT NULL, "end_time" varchar NOT NULL, "appointment_duration" integer DEFAULT 30 NOT NULL, "buffer_time" integer DEFAULT 5, "max_appointments" integer DEFAULT 16 NOT NULL, "is_available" boolean DEFAULT 1, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_ca70b6a810"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctor_availabilities_on_day_of_week" ON "doctor_availabilities" ("day_of_week")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_doctor_availabilities_on_doctor_id_and_day_of_week" ON "doctor_availabilities" ("doctor_id", "day_of_week")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_doctor_availabilities_on_doctor_id" ON "doctor_availabilities" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_doctor_availabilities_on_is_available" ON "doctor_availabilities" ("is_available")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "doctor_availabilities" ("id","doctor_id","day_of_week","start_time","end_time","appointment_duration","buffer_time","max_appointments","is_available","created_at","updated_at")
                     SELECT "id","doctor_id","day_of_week","start_time","end_time","appointment_duration","buffer_time","max_appointments","is_available","created_at","updated_at" FROM "adoctor_availabilities"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "adoctor_availabilities"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "adoctor_verifications" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "verification_type" varchar, "status" integer, "submitted_at" datetime(6), "reviewed_at" datetime(6), "reviewer_notes" text, "documents" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_adoctor_verifications_on_doctor_id" ON "adoctor_verifications" ("doctor_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "adoctor_verifications" ("id","doctor_id","verification_type","status","submitted_at","reviewed_at","reviewer_notes","documents","created_at","updated_at")
                     SELECT "id","doctor_id","verification_type","status","submitted_at","reviewed_at","reviewer_notes","documents","created_at","updated_at" FROM "doctor_verifications"[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE "doctor_verifications"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "doctor_verifications" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "verification_type" varchar, "status" integer, "submitted_at" datetime(6), "reviewed_at" datetime(6), "reviewer_notes" text, "documents" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_7ea1e54b91"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_doctor_verifications_on_doctor_id" ON "doctor_verifications" ("doctor_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "doctor_verifications" ("id","doctor_id","verification_type","status","submitted_at","reviewed_at","reviewer_notes","documents","created_at","updated_at")
                     SELECT "id","doctor_id","verification_type","status","submitted_at","reviewed_at","reviewer_notes","documents","created_at","updated_at" FROM "adoctor_verifications"[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE "adoctor_verifications"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "adoctors" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "license_number" varchar NOT NULL, "specialization" varchar NOT NULL, "sub_specializations" text, "graduated_university" varchar NOT NULL, "graduation_year" integer NOT NULL, "current_institution" varchar, "years_experience" integer DEFAULT 0, "consultation_fee_day" decimal(8,2) NOT NULL, "consultation_fee_night" decimal(8,2) NOT NULL, "consultation_fee_emergency" decimal(8,2) NOT NULL, "languages_spoken" text, "bio" text, "verification_status" integer DEFAULT 0, "rating" decimal(3,2), "total_consultations" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_adoctors_on_verification_status" ON "adoctors" ("verification_status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_adoctors_on_user_id" ON "adoctors" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_adoctors_on_specialization" ON "adoctors" ("specialization")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_adoctors_on_rating" ON "adoctors" ("rating")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_adoctors_on_license_number" ON "adoctors" ("license_number")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "adoctors" ("id","user_id","license_number","specialization","sub_specializations","graduated_university","graduation_year","current_institution","years_experience","consultation_fee_day","consultation_fee_night","consultation_fee_emergency","languages_spoken","bio","verification_status","rating","total_consultations","created_at","updated_at")
                     SELECT "id","user_id","license_number","specialization","sub_specializations","graduated_university","graduation_year","current_institution","years_experience","consultation_fee_day","consultation_fee_night","consultation_fee_emergency","languages_spoken","bio","verification_status","rating","total_consultations","created_at","updated_at" FROM "doctors"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "doctors"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "doctors" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "license_number" varchar NOT NULL, "specialization" varchar NOT NULL, "sub_specializations" text, "graduated_university" varchar NOT NULL, "graduation_year" integer NOT NULL, "current_institution" varchar, "years_experience" integer DEFAULT 0, "consultation_fee_day" decimal(8,2) NOT NULL, "consultation_fee_night" decimal(8,2) NOT NULL, "consultation_fee_emergency" decimal(8,2) NOT NULL, "languages_spoken" text, "bio" text, "verification_status" integer DEFAULT 0, "rating" decimal(3,2), "total_consultations" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_899b01ef33"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_doctors_on_license_number" ON "doctors" ("license_number")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_rating" ON "doctors" ("rating")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_specialization" ON "doctors" ("specialization")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_user_id" ON "doctors" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_doctors_on_verification_status" ON "doctors" ("verification_status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "doctors" ("id","user_id","license_number","specialization","sub_specializations","graduated_university","graduation_year","current_institution","years_experience","consultation_fee_day","consultation_fee_night","consultation_fee_emergency","languages_spoken","bio","verification_status","rating","total_consultations","created_at","updated_at")
                     SELECT "id","user_id","license_number","specialization","sub_specializations","graduated_university","graduation_year","current_institution","years_experience","consultation_fee_day","consultation_fee_night","consultation_fee_emergency","languages_spoken","bio","verification_status","rating","total_consultations","created_at","updated_at" FROM "adoctors"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "adoctors"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.2ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "agroup_sessions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "host_id" integer NOT NULL, "title" varchar NOT NULL, "description" text NOT NULL, "session_type" varchar NOT NULL, "scheduled_at" datetime(6) NOT NULL, "ends_at" datetime(6) NOT NULL, "duration_minutes" integer NOT NULL, "max_participants" integer DEFAULT 10, "current_participants" integer DEFAULT 0, "status" integer DEFAULT 0 NOT NULL, "price" decimal(8,2) DEFAULT 0.0, "currency" varchar DEFAULT 'TRY', "meeting_link" varchar, "meeting_id" varchar, "meeting_password" varchar, "started_at" datetime(6), "ended_at" datetime(6), "session_notes" text, "materials" json, "recording_enabled" boolean DEFAULT 0, "recording_url" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_agroup_sessions_on_status" ON "agroup_sessions" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_agroup_sessions_on_session_type" ON "agroup_sessions" ("session_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_agroup_sessions_on_scheduled_at" ON "agroup_sessions" ("scheduled_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_agroup_sessions_on_host_id" ON "agroup_sessions" ("host_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_agroup_sessions_on_host_id_and_scheduled_at" ON "agroup_sessions" ("host_id", "scheduled_at")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "agroup_sessions" ("id","host_id","title","description","session_type","scheduled_at","ends_at","duration_minutes","max_participants","current_participants","status","price","currency","meeting_link","meeting_id","meeting_password","started_at","ended_at","session_notes","materials","recording_enabled","recording_url","created_at","updated_at")
                     SELECT "id","host_id","title","description","session_type","scheduled_at","ends_at","duration_minutes","max_participants","current_participants","status","price","currency","meeting_link","meeting_id","meeting_password","started_at","ended_at","session_notes","materials","recording_enabled","recording_url","created_at","updated_at" FROM "group_sessions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "group_sessions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "group_sessions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "host_id" integer NOT NULL, "title" varchar NOT NULL, "description" text NOT NULL, "session_type" varchar NOT NULL, "scheduled_at" datetime(6) NOT NULL, "ends_at" datetime(6) NOT NULL, "duration_minutes" integer NOT NULL, "max_participants" integer DEFAULT 10, "current_participants" integer DEFAULT 0, "status" integer DEFAULT 0 NOT NULL, "price" decimal(8,2) DEFAULT 0.0, "currency" varchar DEFAULT 'TRY', "meeting_link" varchar, "meeting_id" varchar, "meeting_password" varchar, "started_at" datetime(6), "ended_at" datetime(6), "session_notes" text, "materials" json, "recording_enabled" boolean DEFAULT 0, "recording_url" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_f149660cd5"
FOREIGN KEY ("host_id")
  REFERENCES "doctors" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_host_id_and_scheduled_at" ON "group_sessions" ("host_id", "scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_host_id" ON "group_sessions" ("host_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_scheduled_at" ON "group_sessions" ("scheduled_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_session_type" ON "group_sessions" ("session_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_group_sessions_on_status" ON "group_sessions" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "group_sessions" ("id","host_id","title","description","session_type","scheduled_at","ends_at","duration_minutes","max_participants","current_participants","status","price","currency","meeting_link","meeting_id","meeting_password","started_at","ended_at","session_notes","materials","recording_enabled","recording_url","created_at","updated_at")
                     SELECT "id","host_id","title","description","session_type","scheduled_at","ends_at","duration_minutes","max_participants","current_participants","status","price","currency","meeting_link","meeting_id","meeting_password","started_at","ended_at","session_notes","materials","recording_enabled","recording_url","created_at","updated_at" FROM "agroup_sessions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "agroup_sessions"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "ahealth_metrics" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "trackable_type" varchar, "trackable_id" integer, "metric_type" varchar NOT NULL, "value" decimal(10,4) NOT NULL, "unit" varchar NOT NULL, "recorded_at" datetime(6) NOT NULL, "source" varchar NOT NULL, "notes" text, "metadata" json, "device_id" varchar, "reference_min" decimal(10,4), "reference_max" decimal(10,4), "is_abnormal" boolean DEFAULT 0, "trend" varchar, "quality_score" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ahealth_metrics_on_trackable_type_and_trackable_id" ON "ahealth_metrics" ("trackable_type", "trackable_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ahealth_metrics_on_trackable" ON "ahealth_metrics" ("trackable_type", "trackable_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ahealth_metrics_on_source" ON "ahealth_metrics" ("source")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ahealth_metrics_on_recorded_at" ON "ahealth_metrics" ("recorded_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_ahealth_metrics_on_patient_id" ON "ahealth_metrics" ("patient_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "tidx_on_patient_id_metric_type_recorded_at_d2a57a400b" ON "ahealth_metrics" ("patient_id", "metric_type", "recorded_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ahealth_metrics_on_metric_type" ON "ahealth_metrics" ("metric_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ahealth_metrics_on_is_abnormal" ON "ahealth_metrics" ("is_abnormal")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "ahealth_metrics" ("id","patient_id","trackable_type","trackable_id","metric_type","value","unit","recorded_at","source","notes","metadata","device_id","reference_min","reference_max","is_abnormal","trend","quality_score","created_at","updated_at")
                     SELECT "id","patient_id","trackable_type","trackable_id","metric_type","value","unit","recorded_at","source","notes","metadata","device_id","reference_min","reference_max","is_abnormal","trend","quality_score","created_at","updated_at" FROM "health_metrics"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "health_metrics"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "health_metrics" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "trackable_type" varchar, "trackable_id" integer, "metric_type" varchar NOT NULL, "value" decimal(10,4) NOT NULL, "unit" varchar NOT NULL, "recorded_at" datetime(6) NOT NULL, "source" varchar NOT NULL, "notes" text, "metadata" json, "device_id" varchar, "reference_min" decimal(10,4), "reference_max" decimal(10,4), "is_abnormal" boolean DEFAULT 0, "trend" varchar, "quality_score" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_fcc1125f48"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_is_abnormal" ON "health_metrics" ("is_abnormal")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_metric_type" ON "health_metrics" ("metric_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "idx_on_patient_id_metric_type_recorded_at_d2a57a400b" ON "health_metrics" ("patient_id", "metric_type", "recorded_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_patient_id" ON "health_metrics" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_recorded_at" ON "health_metrics" ("recorded_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_source" ON "health_metrics" ("source")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_trackable" ON "health_metrics" ("trackable_type", "trackable_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_health_metrics_on_trackable_type_and_trackable_id" ON "health_metrics" ("trackable_type", "trackable_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "health_metrics" ("id","patient_id","trackable_type","trackable_id","metric_type","value","unit","recorded_at","source","notes","metadata","device_id","reference_min","reference_max","is_abnormal","trend","quality_score","created_at","updated_at")
                     SELECT "id","patient_id","trackable_type","trackable_id","metric_type","value","unit","recorded_at","source","notes","metadata","device_id","reference_min","reference_max","is_abnormal","trend","quality_score","created_at","updated_at" FROM "ahealth_metrics"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "ahealth_metrics"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "ahealth_profiles" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "height" decimal, "weight" decimal, "allergies" text, "chronic_conditions" text, "medications" text, "family_history" text, "lifestyle_notes" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ahealth_profiles_on_patient_id" ON "ahealth_profiles" ("patient_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "ahealth_profiles" ("id","patient_id","height","weight","allergies","chronic_conditions","medications","family_history","lifestyle_notes","created_at","updated_at")
                     SELECT "id","patient_id","height","weight","allergies","chronic_conditions","medications","family_history","lifestyle_notes","created_at","updated_at" FROM "health_profiles"[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE "health_profiles"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "health_profiles" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "height" decimal, "weight" decimal, "allergies" text, "chronic_conditions" text, "medications" text, "family_history" text, "lifestyle_notes" text, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_9630807551"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_health_profiles_on_patient_id" ON "health_profiles" ("patient_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "health_profiles" ("id","patient_id","height","weight","allergies","chronic_conditions","medications","family_history","lifestyle_notes","created_at","updated_at")
                     SELECT "id","patient_id","height","weight","allergies","chronic_conditions","medications","family_history","lifestyle_notes","created_at","updated_at" FROM "ahealth_profiles"[0m
  [1m[35m (0.0ms)[0m  [1m[35mDROP TABLE "ahealth_profiles"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "amedical_documents" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "issued_by_id" integer, "consultation_id" integer, "document_type" varchar NOT NULL, "title" varchar NOT NULL, "description" text, "file_url" varchar NOT NULL, "file_name" varchar, "file_type" varchar, "file_size_bytes" bigint, "file_hash" varchar, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6), "status" integer DEFAULT 0 NOT NULL, "is_sensitive" boolean DEFAULT 0, "access_permissions" json, "metadata" json, "external_id" varchar, "institution_name" varchar, "last_accessed_at" datetime(6), "access_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_status" ON "amedical_documents" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_patient_id" ON "amedical_documents" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_patient_id_and_document_type" ON "amedical_documents" ("patient_id", "document_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_issued_by_id" ON "amedical_documents" ("issued_by_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_issued_at" ON "amedical_documents" ("issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_is_sensitive" ON "amedical_documents" ("is_sensitive")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_amedical_documents_on_file_hash" ON "amedical_documents" ("file_hash")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_expires_at" ON "amedical_documents" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_document_type" ON "amedical_documents" ("document_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_consultation_id" ON "amedical_documents" ("consultation_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "amedical_documents" ("id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at")
                     SELECT "id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at" FROM "medical_documents"[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE "medical_documents"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "medical_documents" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "issued_by_id" integer, "consultation_id" integer, "document_type" varchar NOT NULL, "title" varchar NOT NULL, "description" text, "file_url" varchar NOT NULL, "file_name" varchar, "file_type" varchar, "file_size_bytes" bigint, "file_hash" varchar, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6), "status" integer DEFAULT 0 NOT NULL, "is_sensitive" boolean DEFAULT 0, "access_permissions" json, "metadata" json, "external_id" varchar, "institution_name" varchar, "last_accessed_at" datetime(6), "access_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_28782abca9"
FOREIGN KEY ("consultation_id")
  REFERENCES "consultations" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_consultation_id" ON "medical_documents" ("consultation_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_document_type" ON "medical_documents" ("document_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_expires_at" ON "medical_documents" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_medical_documents_on_file_hash" ON "medical_documents" ("file_hash")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_is_sensitive" ON "medical_documents" ("is_sensitive")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_issued_at" ON "medical_documents" ("issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_issued_by_id" ON "medical_documents" ("issued_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_patient_id_and_document_type" ON "medical_documents" ("patient_id", "document_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_patient_id" ON "medical_documents" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_status" ON "medical_documents" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "medical_documents" ("id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at")
                     SELECT "id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at" FROM "amedical_documents"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "amedical_documents"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "amedical_documents" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "issued_by_id" integer, "consultation_id" integer, "document_type" varchar NOT NULL, "title" varchar NOT NULL, "description" text, "file_url" varchar NOT NULL, "file_name" varchar, "file_type" varchar, "file_size_bytes" bigint, "file_hash" varchar, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6), "status" integer DEFAULT 0 NOT NULL, "is_sensitive" boolean DEFAULT 0, "access_permissions" json, "metadata" json, "external_id" varchar, "institution_name" varchar, "last_accessed_at" datetime(6), "access_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_status" ON "amedical_documents" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_patient_id" ON "amedical_documents" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_patient_id_and_document_type" ON "amedical_documents" ("patient_id", "document_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_issued_by_id" ON "amedical_documents" ("issued_by_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_issued_at" ON "amedical_documents" ("issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_is_sensitive" ON "amedical_documents" ("is_sensitive")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_amedical_documents_on_file_hash" ON "amedical_documents" ("file_hash")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_expires_at" ON "amedical_documents" ("expires_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_document_type" ON "amedical_documents" ("document_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_consultation_id" ON "amedical_documents" ("consultation_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "amedical_documents" ("id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at")
                     SELECT "id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at" FROM "medical_documents"[0m
  [1m[35m (2.7ms)[0m  [1m[35mDROP TABLE "medical_documents"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "medical_documents" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "issued_by_id" integer, "consultation_id" integer, "document_type" varchar NOT NULL, "title" varchar NOT NULL, "description" text, "file_url" varchar NOT NULL, "file_name" varchar, "file_type" varchar, "file_size_bytes" bigint, "file_hash" varchar, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6), "status" integer DEFAULT 0 NOT NULL, "is_sensitive" boolean DEFAULT 0, "access_permissions" json, "metadata" json, "external_id" varchar, "institution_name" varchar, "last_accessed_at" datetime(6), "access_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_28782abca9"
FOREIGN KEY ("consultation_id")
  REFERENCES "consultations" ("id")
, CONSTRAINT "fk_rails_32eea0a133"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_consultation_id" ON "medical_documents" ("consultation_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_document_type" ON "medical_documents" ("document_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_expires_at" ON "medical_documents" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_medical_documents_on_file_hash" ON "medical_documents" ("file_hash")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_is_sensitive" ON "medical_documents" ("is_sensitive")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_issued_at" ON "medical_documents" ("issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_issued_by_id" ON "medical_documents" ("issued_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_patient_id_and_document_type" ON "medical_documents" ("patient_id", "document_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_patient_id" ON "medical_documents" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_status" ON "medical_documents" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "medical_documents" ("id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at")
                     SELECT "id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at" FROM "amedical_documents"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "amedical_documents"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "amedical_documents" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "issued_by_id" integer, "consultation_id" integer, "document_type" varchar NOT NULL, "title" varchar NOT NULL, "description" text, "file_url" varchar NOT NULL, "file_name" varchar, "file_type" varchar, "file_size_bytes" bigint, "file_hash" varchar, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6), "status" integer DEFAULT 0 NOT NULL, "is_sensitive" boolean DEFAULT 0, "access_permissions" json, "metadata" json, "external_id" varchar, "institution_name" varchar, "last_accessed_at" datetime(6), "access_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_status" ON "amedical_documents" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_patient_id" ON "amedical_documents" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_patient_id_and_document_type" ON "amedical_documents" ("patient_id", "document_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_issued_by_id" ON "amedical_documents" ("issued_by_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_issued_at" ON "amedical_documents" ("issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_is_sensitive" ON "amedical_documents" ("is_sensitive")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_amedical_documents_on_file_hash" ON "amedical_documents" ("file_hash")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_expires_at" ON "amedical_documents" ("expires_at")[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_document_type" ON "amedical_documents" ("document_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_documents_on_consultation_id" ON "amedical_documents" ("consultation_id")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "amedical_documents" ("id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at")
                     SELECT "id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at" FROM "medical_documents"[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE "medical_documents"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "medical_documents" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "issued_by_id" integer, "consultation_id" integer, "document_type" varchar NOT NULL, "title" varchar NOT NULL, "description" text, "file_url" varchar NOT NULL, "file_name" varchar, "file_type" varchar, "file_size_bytes" bigint, "file_hash" varchar, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6), "status" integer DEFAULT 0 NOT NULL, "is_sensitive" boolean DEFAULT 0, "access_permissions" json, "metadata" json, "external_id" varchar, "institution_name" varchar, "last_accessed_at" datetime(6), "access_count" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_32eea0a133"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
, CONSTRAINT "fk_rails_28782abca9"
FOREIGN KEY ("consultation_id")
  REFERENCES "consultations" ("id")
, CONSTRAINT "fk_rails_9505f7c2d6"
FOREIGN KEY ("issued_by_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_consultation_id" ON "medical_documents" ("consultation_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_document_type" ON "medical_documents" ("document_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_expires_at" ON "medical_documents" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_medical_documents_on_file_hash" ON "medical_documents" ("file_hash")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_is_sensitive" ON "medical_documents" ("is_sensitive")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_issued_at" ON "medical_documents" ("issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_issued_by_id" ON "medical_documents" ("issued_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_patient_id_and_document_type" ON "medical_documents" ("patient_id", "document_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_patient_id" ON "medical_documents" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_documents_on_status" ON "medical_documents" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "medical_documents" ("id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at")
                     SELECT "id","patient_id","issued_by_id","consultation_id","document_type","title","description","file_url","file_name","file_type","file_size_bytes","file_hash","issued_at","expires_at","status","is_sensitive","access_permissions","metadata","external_id","institution_name","last_accessed_at","access_count","created_at","updated_at" FROM "amedical_documents"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "amedical_documents"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "amedical_licenses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "license_number" varchar NOT NULL, "issuing_authority" varchar NOT NULL, "issue_date" date NOT NULL, "expiry_date" date NOT NULL, "license_type" varchar NOT NULL, "status" integer DEFAULT 0, "document_url" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_licenses_on_status" ON "amedical_licenses" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_licenses_on_license_type" ON "amedical_licenses" ("license_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_amedical_licenses_on_license_number" ON "amedical_licenses" ("license_number")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_licenses_on_expiry_date" ON "amedical_licenses" ("expiry_date")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_amedical_licenses_on_doctor_id" ON "amedical_licenses" ("doctor_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "amedical_licenses" ("id","doctor_id","license_number","issuing_authority","issue_date","expiry_date","license_type","status","document_url","created_at","updated_at")
                     SELECT "id","doctor_id","license_number","issuing_authority","issue_date","expiry_date","license_type","status","document_url","created_at","updated_at" FROM "medical_licenses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "medical_licenses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "medical_licenses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "doctor_id" integer NOT NULL, "license_number" varchar NOT NULL, "issuing_authority" varchar NOT NULL, "issue_date" date NOT NULL, "expiry_date" date NOT NULL, "license_type" varchar NOT NULL, "status" integer DEFAULT 0, "document_url" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_ecc8c5f96a"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_doctor_id" ON "medical_licenses" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_expiry_date" ON "medical_licenses" ("expiry_date")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_medical_licenses_on_license_number" ON "medical_licenses" ("license_number")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_license_type" ON "medical_licenses" ("license_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_medical_licenses_on_status" ON "medical_licenses" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "medical_licenses" ("id","doctor_id","license_number","issuing_authority","issue_date","expiry_date","license_type","status","document_url","created_at","updated_at")
                     SELECT "id","doctor_id","license_number","issuing_authority","issue_date","expiry_date","license_type","status","document_url","created_at","updated_at" FROM "amedical_licenses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "amedical_licenses"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.2ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "anotifications" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "notifiable_type" varchar, "notifiable_id" integer, "notification_type" varchar NOT NULL, "title" varchar NOT NULL, "message" text NOT NULL, "action_text" text, "action_url" varchar, "read_at" datetime(6), "sent_at" datetime(6) NOT NULL, "priority" integer DEFAULT 1 NOT NULL, "delivery_method" varchar, "delivered" boolean DEFAULT 0, "delivered_at" datetime(6), "delivery_metadata" json, "expires_at" datetime(6), "icon" varchar, "color" varchar, "data" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_anotifications_on_user_id" ON "anotifications" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_anotifications_on_user_id_and_read_at" ON "anotifications" ("user_id", "read_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_anotifications_on_sent_at" ON "anotifications" ("sent_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_anotifications_on_read_at" ON "anotifications" ("read_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_anotifications_on_priority" ON "anotifications" ("priority")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_anotifications_on_notification_type" ON "anotifications" ("notification_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_anotifications_on_notifiable_type_and_notifiable_id" ON "anotifications" ("notifiable_type", "notifiable_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_anotifications_on_notifiable" ON "anotifications" ("notifiable_type", "notifiable_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_anotifications_on_expires_at" ON "anotifications" ("expires_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_anotifications_on_delivered" ON "anotifications" ("delivered")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "anotifications" ("id","user_id","notifiable_type","notifiable_id","notification_type","title","message","action_text","action_url","read_at","sent_at","priority","delivery_method","delivered","delivered_at","delivery_metadata","expires_at","icon","color","data","created_at","updated_at")
                     SELECT "id","user_id","notifiable_type","notifiable_id","notification_type","title","message","action_text","action_url","read_at","sent_at","priority","delivery_method","delivered","delivered_at","delivery_metadata","expires_at","icon","color","data","created_at","updated_at" FROM "notifications"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "notifications"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "notifications" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "notifiable_type" varchar, "notifiable_id" integer, "notification_type" varchar NOT NULL, "title" varchar NOT NULL, "message" text NOT NULL, "action_text" text, "action_url" varchar, "read_at" datetime(6), "sent_at" datetime(6) NOT NULL, "priority" integer DEFAULT 1 NOT NULL, "delivery_method" varchar, "delivered" boolean DEFAULT 0, "delivered_at" datetime(6), "delivery_metadata" json, "expires_at" datetime(6), "icon" varchar, "color" varchar, "data" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_b080fb4855"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_delivered" ON "notifications" ("delivered")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_expires_at" ON "notifications" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_notifiable" ON "notifications" ("notifiable_type", "notifiable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_notifiable_type_and_notifiable_id" ON "notifications" ("notifiable_type", "notifiable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_notification_type" ON "notifications" ("notification_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_priority" ON "notifications" ("priority")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_read_at" ON "notifications" ("read_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_sent_at" ON "notifications" ("sent_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_user_id_and_read_at" ON "notifications" ("user_id", "read_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_notifications_on_user_id" ON "notifications" ("user_id")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "notifications" ("id","user_id","notifiable_type","notifiable_id","notification_type","title","message","action_text","action_url","read_at","sent_at","priority","delivery_method","delivered","delivered_at","delivery_metadata","expires_at","icon","color","data","created_at","updated_at")
                     SELECT "id","user_id","notifiable_type","notifiable_id","notification_type","title","message","action_text","action_url","read_at","sent_at","priority","delivery_method","delivered","delivered_at","delivery_metadata","expires_at","icon","color","data","created_at","updated_at" FROM "anotifications"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "anotifications"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aorder_items" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order_id" integer NOT NULL, "orderable_type" varchar NOT NULL, "orderable_id" integer NOT NULL, "quantity" integer DEFAULT 1 NOT NULL, "unit_price" decimal(10,2) NOT NULL, "total_price" decimal(10,2) NOT NULL, "item_name" varchar NOT NULL, "item_description" text, "item_metadata" json, "discount_amount" decimal(10,2) DEFAULT 0.0, "discount_type" varchar, "is_refundable" boolean DEFAULT 1, "delivered_at" datetime(6), "status" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aorder_items_on_status" ON "aorder_items" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aorder_items_on_orderable_type_and_orderable_id" ON "aorder_items" ("orderable_type", "orderable_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aorder_items_on_orderable" ON "aorder_items" ("orderable_type", "orderable_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aorder_items_on_order_id" ON "aorder_items" ("order_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aorder_items" ("id","order_id","orderable_type","orderable_id","quantity","unit_price","total_price","item_name","item_description","item_metadata","discount_amount","discount_type","is_refundable","delivered_at","status","created_at","updated_at")
                     SELECT "id","order_id","orderable_type","orderable_id","quantity","unit_price","total_price","item_name","item_description","item_metadata","discount_amount","discount_type","is_refundable","delivered_at","status","created_at","updated_at" FROM "order_items"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "order_items"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "order_items" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order_id" integer NOT NULL, "orderable_type" varchar NOT NULL, "orderable_id" integer NOT NULL, "quantity" integer DEFAULT 1 NOT NULL, "unit_price" decimal(10,2) NOT NULL, "total_price" decimal(10,2) NOT NULL, "item_name" varchar NOT NULL, "item_description" text, "item_metadata" json, "discount_amount" decimal(10,2) DEFAULT 0.0, "discount_type" varchar, "is_refundable" boolean DEFAULT 1, "delivered_at" datetime(6), "status" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_e3cb28f071"
FOREIGN KEY ("order_id")
  REFERENCES "orders" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_order_id" ON "order_items" ("order_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_orderable" ON "order_items" ("orderable_type", "orderable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_orderable_type_and_orderable_id" ON "order_items" ("orderable_type", "orderable_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_order_items_on_status" ON "order_items" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "order_items" ("id","order_id","orderable_type","orderable_id","quantity","unit_price","total_price","item_name","item_description","item_metadata","discount_amount","discount_type","is_refundable","delivered_at","status","created_at","updated_at")
                     SELECT "id","order_id","orderable_type","orderable_id","quantity","unit_price","total_price","item_name","item_description","item_metadata","discount_amount","discount_type","is_refundable","delivered_at","status","created_at","updated_at" FROM "aorder_items"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aorder_items"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.1ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aorders" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "order_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "subtotal_amount" decimal(10,2) NOT NULL, "tax_amount" decimal(10,2) DEFAULT 0.0, "discount_amount" decimal(10,2) DEFAULT 0.0, "total_amount" decimal(10,2) NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "order_type" varchar, "billing_address" json, "discount_code" varchar, "notes" text, "confirmed_at" datetime(6), "shipped_at" datetime(6), "delivered_at" datetime(6), "cancelled_at" datetime(6), "cancellation_reason" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aorders_on_user_id" ON "aorders" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aorders_on_user_id_and_status" ON "aorders" ("user_id", "status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aorders_on_status" ON "aorders" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aorders_on_order_type" ON "aorders" ("order_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_aorders_on_order_number" ON "aorders" ("order_number")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aorders_on_confirmed_at" ON "aorders" ("confirmed_at")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aorders" ("id","user_id","order_number","status","subtotal_amount","tax_amount","discount_amount","total_amount","currency","order_type","billing_address","discount_code","notes","confirmed_at","shipped_at","delivered_at","cancelled_at","cancellation_reason","created_at","updated_at")
                     SELECT "id","user_id","order_number","status","subtotal_amount","tax_amount","discount_amount","total_amount","currency","order_type","billing_address","discount_code","notes","confirmed_at","shipped_at","delivered_at","cancelled_at","cancellation_reason","created_at","updated_at" FROM "orders"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "orders"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "orders" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "order_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "subtotal_amount" decimal(10,2) NOT NULL, "tax_amount" decimal(10,2) DEFAULT 0.0, "discount_amount" decimal(10,2) DEFAULT 0.0, "total_amount" decimal(10,2) NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "order_type" varchar, "billing_address" json, "discount_code" varchar, "notes" text, "confirmed_at" datetime(6), "shipped_at" datetime(6), "delivered_at" datetime(6), "cancelled_at" datetime(6), "cancellation_reason" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_f868b47f6a"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_orders_on_confirmed_at" ON "orders" ("confirmed_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_orders_on_order_number" ON "orders" ("order_number")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_orders_on_order_type" ON "orders" ("order_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_orders_on_status" ON "orders" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_orders_on_user_id_and_status" ON "orders" ("user_id", "status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_orders_on_user_id" ON "orders" ("user_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "orders" ("id","user_id","order_number","status","subtotal_amount","tax_amount","discount_amount","total_amount","currency","order_type","billing_address","discount_code","notes","confirmed_at","shipped_at","delivered_at","cancelled_at","cancellation_reason","created_at","updated_at")
                     SELECT "id","user_id","order_number","status","subtotal_amount","tax_amount","discount_amount","total_amount","currency","order_type","billing_address","discount_code","notes","confirmed_at","shipped_at","delivered_at","cancelled_at","cancellation_reason","created_at","updated_at" FROM "aorders"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aorders"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "apatients" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "medical_record_number" varchar NOT NULL, "emergency_contact_name" varchar NOT NULL, "emergency_contact_phone" varchar NOT NULL, "blood_type" varchar, "pregnancy_count" integer DEFAULT 0, "birth_count" integer DEFAULT 0, "smoking_status" integer DEFAULT 0, "alcohol_consumption" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_apatients_on_user_id" ON "apatients" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_apatients_on_medical_record_number" ON "apatients" ("medical_record_number")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_apatients_on_blood_type" ON "apatients" ("blood_type")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "apatients" ("id","user_id","medical_record_number","emergency_contact_name","emergency_contact_phone","blood_type","pregnancy_count","birth_count","smoking_status","alcohol_consumption","created_at","updated_at")
                     SELECT "id","user_id","medical_record_number","emergency_contact_name","emergency_contact_phone","blood_type","pregnancy_count","birth_count","smoking_status","alcohol_consumption","created_at","updated_at" FROM "patients"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "patients"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "patients" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "medical_record_number" varchar NOT NULL, "emergency_contact_name" varchar NOT NULL, "emergency_contact_phone" varchar NOT NULL, "blood_type" varchar, "pregnancy_count" integer DEFAULT 0, "birth_count" integer DEFAULT 0, "smoking_status" integer DEFAULT 0, "alcohol_consumption" integer DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_623f05c630"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_patients_on_blood_type" ON "patients" ("blood_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_patients_on_medical_record_number" ON "patients" ("medical_record_number")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_patients_on_user_id" ON "patients" ("user_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "patients" ("id","user_id","medical_record_number","emergency_contact_name","emergency_contact_phone","blood_type","pregnancy_count","birth_count","smoking_status","alcohol_consumption","created_at","updated_at")
                     SELECT "id","user_id","medical_record_number","emergency_contact_name","emergency_contact_phone","blood_type","pregnancy_count","birth_count","smoking_status","alcohol_consumption","created_at","updated_at" FROM "apatients"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "apatients"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "apayment_methods" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "payment_type" varchar NOT NULL, "provider" varchar NOT NULL, "token" varchar NOT NULL, "last_four" varchar NOT NULL, "cardholder_name" varchar, "expires_at" date, "billing_address_line1" varchar, "billing_address_line2" varchar, "billing_city" varchar, "billing_state" varchar, "billing_postal_code" varchar, "billing_country" varchar DEFAULT 'TR', "is_default" boolean DEFAULT 0, "active" boolean DEFAULT 1, "verified_at" datetime(6), "metadata" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayment_methods_on_user_id" ON "apayment_methods" ("user_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_apayment_methods_on_user_id_and_is_default" ON "apayment_methods" ("user_id", "is_default")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayment_methods_on_provider" ON "apayment_methods" ("provider")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_apayment_methods_on_payment_type" ON "apayment_methods" ("payment_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayment_methods_on_is_default" ON "apayment_methods" ("is_default")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayment_methods_on_active" ON "apayment_methods" ("active")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "apayment_methods" ("id","user_id","payment_type","provider","token","last_four","cardholder_name","expires_at","billing_address_line1","billing_address_line2","billing_city","billing_state","billing_postal_code","billing_country","is_default","active","verified_at","metadata","created_at","updated_at")
                     SELECT "id","user_id","payment_type","provider","token","last_four","cardholder_name","expires_at","billing_address_line1","billing_address_line2","billing_city","billing_state","billing_postal_code","billing_country","is_default","active","verified_at","metadata","created_at","updated_at" FROM "payment_methods"[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE "payment_methods"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "payment_methods" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "payment_type" varchar NOT NULL, "provider" varchar NOT NULL, "token" varchar NOT NULL, "last_four" varchar NOT NULL, "cardholder_name" varchar, "expires_at" date, "billing_address_line1" varchar, "billing_address_line2" varchar, "billing_city" varchar, "billing_state" varchar, "billing_postal_code" varchar, "billing_country" varchar DEFAULT 'TR', "is_default" boolean DEFAULT 0, "active" boolean DEFAULT 1, "verified_at" datetime(6), "metadata" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_e13d4c515f"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_active" ON "payment_methods" ("active")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_is_default" ON "payment_methods" ("is_default")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_payment_type" ON "payment_methods" ("payment_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_provider" ON "payment_methods" ("provider")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_user_id_and_is_default" ON "payment_methods" ("user_id", "is_default")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_payment_methods_on_user_id" ON "payment_methods" ("user_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "payment_methods" ("id","user_id","payment_type","provider","token","last_four","cardholder_name","expires_at","billing_address_line1","billing_address_line2","billing_city","billing_state","billing_postal_code","billing_country","is_default","active","verified_at","metadata","created_at","updated_at")
                     SELECT "id","user_id","payment_type","provider","token","last_four","cardholder_name","expires_at","billing_address_line1","billing_address_line2","billing_city","billing_state","billing_postal_code","billing_country","is_default","active","verified_at","metadata","created_at","updated_at" FROM "apayment_methods"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "apayment_methods"[0m
  [1m[35m (0.2ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "apayments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order_id" integer NOT NULL, "payment_method_id" integer NOT NULL, "amount" decimal(10,2) NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "status" integer DEFAULT 0 NOT NULL, "transaction_id" varchar, "gateway_transaction_id" varchar, "payment_gateway" varchar, "processed_at" datetime(6), "failed_at" datetime(6), "refunded_at" datetime(6), "refunded_amount" decimal(10,2) DEFAULT 0.0, "failure_reason" text, "gateway_response" text, "gateway_metadata" json, "receipt_url" varchar, "is_test" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_apayments_on_transaction_id" ON "apayments" ("transaction_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_status" ON "apayments" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_processed_at" ON "apayments" ("processed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_payment_method_id" ON "apayments" ("payment_method_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_payment_gateway" ON "apayments" ("payment_gateway")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_order_id" ON "apayments" ("order_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_order_id_and_status" ON "apayments" ("order_id", "status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_gateway_transaction_id" ON "apayments" ("gateway_transaction_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "apayments" ("id","order_id","payment_method_id","amount","currency","status","transaction_id","gateway_transaction_id","payment_gateway","processed_at","failed_at","refunded_at","refunded_amount","failure_reason","gateway_response","gateway_metadata","receipt_url","is_test","created_at","updated_at")
                     SELECT "id","order_id","payment_method_id","amount","currency","status","transaction_id","gateway_transaction_id","payment_gateway","processed_at","failed_at","refunded_at","refunded_amount","failure_reason","gateway_response","gateway_metadata","receipt_url","is_test","created_at","updated_at" FROM "payments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "payments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "payments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order_id" integer NOT NULL, "payment_method_id" integer NOT NULL, "amount" decimal(10,2) NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "status" integer DEFAULT 0 NOT NULL, "transaction_id" varchar, "gateway_transaction_id" varchar, "payment_gateway" varchar, "processed_at" datetime(6), "failed_at" datetime(6), "refunded_at" datetime(6), "refunded_amount" decimal(10,2) DEFAULT 0.0, "failure_reason" text, "gateway_response" text, "gateway_metadata" json, "receipt_url" varchar, "is_test" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_6af949464b"
FOREIGN KEY ("order_id")
  REFERENCES "orders" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_gateway_transaction_id" ON "payments" ("gateway_transaction_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_order_id_and_status" ON "payments" ("order_id", "status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_payments_on_order_id" ON "payments" ("order_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_payments_on_payment_gateway" ON "payments" ("payment_gateway")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_payment_method_id" ON "payments" ("payment_method_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_payments_on_processed_at" ON "payments" ("processed_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_payments_on_status" ON "payments" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_payments_on_transaction_id" ON "payments" ("transaction_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "payments" ("id","order_id","payment_method_id","amount","currency","status","transaction_id","gateway_transaction_id","payment_gateway","processed_at","failed_at","refunded_at","refunded_amount","failure_reason","gateway_response","gateway_metadata","receipt_url","is_test","created_at","updated_at")
                     SELECT "id","order_id","payment_method_id","amount","currency","status","transaction_id","gateway_transaction_id","payment_gateway","processed_at","failed_at","refunded_at","refunded_amount","failure_reason","gateway_response","gateway_metadata","receipt_url","is_test","created_at","updated_at" FROM "apayments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "apayments"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "apayments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order_id" integer NOT NULL, "payment_method_id" integer NOT NULL, "amount" decimal(10,2) NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "status" integer DEFAULT 0 NOT NULL, "transaction_id" varchar, "gateway_transaction_id" varchar, "payment_gateway" varchar, "processed_at" datetime(6), "failed_at" datetime(6), "refunded_at" datetime(6), "refunded_amount" decimal(10,2) DEFAULT 0.0, "failure_reason" text, "gateway_response" text, "gateway_metadata" json, "receipt_url" varchar, "is_test" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_apayments_on_transaction_id" ON "apayments" ("transaction_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_status" ON "apayments" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_processed_at" ON "apayments" ("processed_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_payment_method_id" ON "apayments" ("payment_method_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_payment_gateway" ON "apayments" ("payment_gateway")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_order_id" ON "apayments" ("order_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_order_id_and_status" ON "apayments" ("order_id", "status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apayments_on_gateway_transaction_id" ON "apayments" ("gateway_transaction_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "apayments" ("id","order_id","payment_method_id","amount","currency","status","transaction_id","gateway_transaction_id","payment_gateway","processed_at","failed_at","refunded_at","refunded_amount","failure_reason","gateway_response","gateway_metadata","receipt_url","is_test","created_at","updated_at")
                     SELECT "id","order_id","payment_method_id","amount","currency","status","transaction_id","gateway_transaction_id","payment_gateway","processed_at","failed_at","refunded_at","refunded_amount","failure_reason","gateway_response","gateway_metadata","receipt_url","is_test","created_at","updated_at" FROM "payments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "payments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "payments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order_id" integer NOT NULL, "payment_method_id" integer NOT NULL, "amount" decimal(10,2) NOT NULL, "currency" varchar DEFAULT 'TRY' NOT NULL, "status" integer DEFAULT 0 NOT NULL, "transaction_id" varchar, "gateway_transaction_id" varchar, "payment_gateway" varchar, "processed_at" datetime(6), "failed_at" datetime(6), "refunded_at" datetime(6), "refunded_amount" decimal(10,2) DEFAULT 0.0, "failure_reason" text, "gateway_response" text, "gateway_metadata" json, "receipt_url" varchar, "is_test" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_6af949464b"
FOREIGN KEY ("order_id")
  REFERENCES "orders" ("id")
, CONSTRAINT "fk_rails_d384ec1ebf"
FOREIGN KEY ("payment_method_id")
  REFERENCES "payment_methods" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_gateway_transaction_id" ON "payments" ("gateway_transaction_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_payments_on_order_id_and_status" ON "payments" ("order_id", "status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_order_id" ON "payments" ("order_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_payment_gateway" ON "payments" ("payment_gateway")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_payment_method_id" ON "payments" ("payment_method_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_processed_at" ON "payments" ("processed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_payments_on_status" ON "payments" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_payments_on_transaction_id" ON "payments" ("transaction_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "payments" ("id","order_id","payment_method_id","amount","currency","status","transaction_id","gateway_transaction_id","payment_gateway","processed_at","failed_at","refunded_at","refunded_amount","failure_reason","gateway_response","gateway_metadata","receipt_url","is_test","created_at","updated_at")
                     SELECT "id","order_id","payment_method_id","amount","currency","status","transaction_id","gateway_transaction_id","payment_gateway","processed_at","failed_at","refunded_at","refunded_amount","failure_reason","gateway_response","gateway_metadata","receipt_url","is_test","created_at","updated_at" FROM "apayments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "apayments"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "apregnancy_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "conception_date" date NOT NULL, "due_date" date NOT NULL, "last_menstrual_period" date, "current_week" integer DEFAULT 0, "current_trimester" integer DEFAULT 1, "pre_pregnancy_weight" decimal(5,2), "current_weight" decimal(5,2), "weight_gain" decimal(5,2) DEFAULT 0.0, "fundal_height" decimal(4,1), "fetal_heart_rate" integer, "blood_pressure_systolic" integer, "blood_pressure_diastolic" integer, "glucose_level" decimal(5,2), "iron_level" decimal(5,2), "symptoms" json, "risk_factors" json, "notes" text, "active" boolean DEFAULT 1, "pregnancy_type" varchar DEFAULT 'singleton', "delivery_date" datetime(6), "delivery_type" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_apregnancy_trackers_on_patient_id" ON "apregnancy_trackers" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apregnancy_trackers_on_patient_id_and_active" ON "apregnancy_trackers" ("patient_id", "active")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apregnancy_trackers_on_due_date" ON "apregnancy_trackers" ("due_date")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apregnancy_trackers_on_current_week" ON "apregnancy_trackers" ("current_week")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apregnancy_trackers_on_current_trimester" ON "apregnancy_trackers" ("current_trimester")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_apregnancy_trackers_on_active" ON "apregnancy_trackers" ("active")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "apregnancy_trackers" ("id","patient_id","conception_date","due_date","last_menstrual_period","current_week","current_trimester","pre_pregnancy_weight","current_weight","weight_gain","fundal_height","fetal_heart_rate","blood_pressure_systolic","blood_pressure_diastolic","glucose_level","iron_level","symptoms","risk_factors","notes","active","pregnancy_type","delivery_date","delivery_type","created_at","updated_at")
                     SELECT "id","patient_id","conception_date","due_date","last_menstrual_period","current_week","current_trimester","pre_pregnancy_weight","current_weight","weight_gain","fundal_height","fetal_heart_rate","blood_pressure_systolic","blood_pressure_diastolic","glucose_level","iron_level","symptoms","risk_factors","notes","active","pregnancy_type","delivery_date","delivery_type","created_at","updated_at" FROM "pregnancy_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "pregnancy_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "pregnancy_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "conception_date" date NOT NULL, "due_date" date NOT NULL, "last_menstrual_period" date, "current_week" integer DEFAULT 0, "current_trimester" integer DEFAULT 1, "pre_pregnancy_weight" decimal(5,2), "current_weight" decimal(5,2), "weight_gain" decimal(5,2) DEFAULT 0.0, "fundal_height" decimal(4,1), "fetal_heart_rate" integer, "blood_pressure_systolic" integer, "blood_pressure_diastolic" integer, "glucose_level" decimal(5,2), "iron_level" decimal(5,2), "symptoms" json, "risk_factors" json, "notes" text, "active" boolean DEFAULT 1, "pregnancy_type" varchar DEFAULT 'singleton', "delivery_date" datetime(6), "delivery_type" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_4714aeefbb"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_active" ON "pregnancy_trackers" ("active")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_current_trimester" ON "pregnancy_trackers" ("current_trimester")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_current_week" ON "pregnancy_trackers" ("current_week")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_due_date" ON "pregnancy_trackers" ("due_date")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_patient_id_and_active" ON "pregnancy_trackers" ("patient_id", "active")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_pregnancy_trackers_on_patient_id" ON "pregnancy_trackers" ("patient_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "pregnancy_trackers" ("id","patient_id","conception_date","due_date","last_menstrual_period","current_week","current_trimester","pre_pregnancy_weight","current_weight","weight_gain","fundal_height","fetal_heart_rate","blood_pressure_systolic","blood_pressure_diastolic","glucose_level","iron_level","symptoms","risk_factors","notes","active","pregnancy_type","delivery_date","delivery_type","created_at","updated_at")
                     SELECT "id","patient_id","conception_date","due_date","last_menstrual_period","current_week","current_trimester","pre_pregnancy_weight","current_weight","weight_gain","fundal_height","fetal_heart_rate","blood_pressure_systolic","blood_pressure_diastolic","glucose_level","iron_level","symptoms","risk_factors","notes","active","pregnancy_type","delivery_date","delivery_type","created_at","updated_at" FROM "apregnancy_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "apregnancy_trackers"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aprescriptions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "consultation_id" integer NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "prescription_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "instructions" text, "diagnosis" text, "additional_notes" text, "medications" json, "is_digital" boolean DEFAULT 1, "pharmacy_name" varchar, "pharmacy_address" varchar, "dispensed_at" datetime(6), "dispensed_by" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_status" ON "aprescriptions" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_aprescriptions_on_prescription_number" ON "aprescriptions" ("prescription_number")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_patient_id" ON "aprescriptions" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_patient_id_and_issued_at" ON "aprescriptions" ("patient_id", "issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_issued_at" ON "aprescriptions" ("issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_expires_at" ON "aprescriptions" ("expires_at")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_doctor_id" ON "aprescriptions" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_doctor_id_and_issued_at" ON "aprescriptions" ("doctor_id", "issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_consultation_id" ON "aprescriptions" ("consultation_id")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "aprescriptions" ("id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at")
                     SELECT "id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at" FROM "prescriptions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "prescriptions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "prescriptions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "consultation_id" integer NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "prescription_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "instructions" text, "diagnosis" text, "additional_notes" text, "medications" json, "is_digital" boolean DEFAULT 1, "pharmacy_name" varchar, "pharmacy_address" varchar, "dispensed_at" datetime(6), "dispensed_by" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_280b58d894"
FOREIGN KEY ("consultation_id")
  REFERENCES "consultations" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_consultation_id" ON "prescriptions" ("consultation_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_doctor_id_and_issued_at" ON "prescriptions" ("doctor_id", "issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_doctor_id" ON "prescriptions" ("doctor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_expires_at" ON "prescriptions" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_issued_at" ON "prescriptions" ("issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_patient_id_and_issued_at" ON "prescriptions" ("patient_id", "issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_patient_id" ON "prescriptions" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_prescriptions_on_prescription_number" ON "prescriptions" ("prescription_number")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_status" ON "prescriptions" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "prescriptions" ("id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at")
                     SELECT "id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at" FROM "aprescriptions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aprescriptions"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aprescriptions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "consultation_id" integer NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "prescription_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "instructions" text, "diagnosis" text, "additional_notes" text, "medications" json, "is_digital" boolean DEFAULT 1, "pharmacy_name" varchar, "pharmacy_address" varchar, "dispensed_at" datetime(6), "dispensed_by" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_status" ON "aprescriptions" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_aprescriptions_on_prescription_number" ON "aprescriptions" ("prescription_number")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_patient_id" ON "aprescriptions" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_patient_id_and_issued_at" ON "aprescriptions" ("patient_id", "issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_issued_at" ON "aprescriptions" ("issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_expires_at" ON "aprescriptions" ("expires_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_doctor_id" ON "aprescriptions" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_doctor_id_and_issued_at" ON "aprescriptions" ("doctor_id", "issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_consultation_id" ON "aprescriptions" ("consultation_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "aprescriptions" ("id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at")
                     SELECT "id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at" FROM "prescriptions"[0m
  [1m[35m (0.3ms)[0m  [1m[35mDROP TABLE "prescriptions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "prescriptions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "consultation_id" integer NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "prescription_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "instructions" text, "diagnosis" text, "additional_notes" text, "medications" json, "is_digital" boolean DEFAULT 1, "pharmacy_name" varchar, "pharmacy_address" varchar, "dispensed_at" datetime(6), "dispensed_by" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_280b58d894"
FOREIGN KEY ("consultation_id")
  REFERENCES "consultations" ("id")
, CONSTRAINT "fk_rails_f06942fc64"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_consultation_id" ON "prescriptions" ("consultation_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_doctor_id_and_issued_at" ON "prescriptions" ("doctor_id", "issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_doctor_id" ON "prescriptions" ("doctor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_expires_at" ON "prescriptions" ("expires_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_issued_at" ON "prescriptions" ("issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_patient_id_and_issued_at" ON "prescriptions" ("patient_id", "issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_patient_id" ON "prescriptions" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_prescriptions_on_prescription_number" ON "prescriptions" ("prescription_number")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_status" ON "prescriptions" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "prescriptions" ("id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at")
                     SELECT "id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at" FROM "aprescriptions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aprescriptions"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "aprescriptions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "consultation_id" integer NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "prescription_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "instructions" text, "diagnosis" text, "additional_notes" text, "medications" json, "is_digital" boolean DEFAULT 1, "pharmacy_name" varchar, "pharmacy_address" varchar, "dispensed_at" datetime(6), "dispensed_by" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_status" ON "aprescriptions" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_aprescriptions_on_prescription_number" ON "aprescriptions" ("prescription_number")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_patient_id" ON "aprescriptions" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_patient_id_and_issued_at" ON "aprescriptions" ("patient_id", "issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_issued_at" ON "aprescriptions" ("issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_expires_at" ON "aprescriptions" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_doctor_id" ON "aprescriptions" ("doctor_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_doctor_id_and_issued_at" ON "aprescriptions" ("doctor_id", "issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_aprescriptions_on_consultation_id" ON "aprescriptions" ("consultation_id")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "aprescriptions" ("id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at")
                     SELECT "id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at" FROM "prescriptions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "prescriptions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "prescriptions" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "consultation_id" integer NOT NULL, "patient_id" integer NOT NULL, "doctor_id" integer NOT NULL, "prescription_number" varchar NOT NULL, "status" integer DEFAULT 0 NOT NULL, "issued_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "instructions" text, "diagnosis" text, "additional_notes" text, "medications" json, "is_digital" boolean DEFAULT 1, "pharmacy_name" varchar, "pharmacy_address" varchar, "dispensed_at" datetime(6), "dispensed_by" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_f06942fc64"
FOREIGN KEY ("doctor_id")
  REFERENCES "doctors" ("id")
, CONSTRAINT "fk_rails_280b58d894"
FOREIGN KEY ("consultation_id")
  REFERENCES "consultations" ("id")
, CONSTRAINT "fk_rails_bede94f0a0"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_consultation_id" ON "prescriptions" ("consultation_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_doctor_id_and_issued_at" ON "prescriptions" ("doctor_id", "issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_doctor_id" ON "prescriptions" ("doctor_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_expires_at" ON "prescriptions" ("expires_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_issued_at" ON "prescriptions" ("issued_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_patient_id_and_issued_at" ON "prescriptions" ("patient_id", "issued_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_patient_id" ON "prescriptions" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_prescriptions_on_prescription_number" ON "prescriptions" ("prescription_number")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_prescriptions_on_status" ON "prescriptions" ("status")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "prescriptions" ("id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at")
                     SELECT "id","consultation_id","patient_id","doctor_id","prescription_number","status","issued_at","expires_at","instructions","diagnosis","additional_notes","medications","is_digital","pharmacy_name","pharmacy_address","dispensed_at","dispensed_by","created_at","updated_at" FROM "aprescriptions"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "aprescriptions"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "arisk_assessments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "assessed_by_id" integer, "assessment_type" varchar NOT NULL, "risk_level" varchar NOT NULL, "risk_score" decimal(5,2) NOT NULL, "risk_factors" json, "protective_factors" json, "clinical_notes" text, "recommendations" json, "assessed_at" datetime(6) NOT NULL, "valid_until" datetime(6), "requires_follow_up" boolean DEFAULT 0, "next_assessment_due" datetime(6), "assessment_method" varchar, "assessment_data" json, "status" integer DEFAULT 0, "superseded_by_id" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_valid_until" ON "arisk_assessments" ("valid_until")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_superseded_by_id" ON "arisk_assessments" ("superseded_by_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_risk_level" ON "arisk_assessments" ("risk_level")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_requires_follow_up" ON "arisk_assessments" ("requires_follow_up")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_patient_id" ON "arisk_assessments" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tidx_on_patient_id_assessment_type_assessed_at_c1a3b8d791" ON "arisk_assessments" ("patient_id", "assessment_type", "assessed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_next_assessment_due" ON "arisk_assessments" ("next_assessment_due")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_assessment_type" ON "arisk_assessments" ("assessment_type")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_assessed_by_id" ON "arisk_assessments" ("assessed_by_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_assessed_at" ON "arisk_assessments" ("assessed_at")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "arisk_assessments" ("id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at")
                     SELECT "id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at" FROM "risk_assessments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "risk_assessments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "risk_assessments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "assessed_by_id" integer, "assessment_type" varchar NOT NULL, "risk_level" varchar NOT NULL, "risk_score" decimal(5,2) NOT NULL, "risk_factors" json, "protective_factors" json, "clinical_notes" text, "recommendations" json, "assessed_at" datetime(6) NOT NULL, "valid_until" datetime(6), "requires_follow_up" boolean DEFAULT 0, "next_assessment_due" datetime(6), "assessment_method" varchar, "assessment_data" json, "status" integer DEFAULT 0, "superseded_by_id" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_94a65cd14d"
FOREIGN KEY ("assessed_by_id")
  REFERENCES "doctors" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessed_at" ON "risk_assessments" ("assessed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessed_by_id" ON "risk_assessments" ("assessed_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessment_type" ON "risk_assessments" ("assessment_type")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_next_assessment_due" ON "risk_assessments" ("next_assessment_due")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "idx_on_patient_id_assessment_type_assessed_at_c1a3b8d791" ON "risk_assessments" ("patient_id", "assessment_type", "assessed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_patient_id" ON "risk_assessments" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_requires_follow_up" ON "risk_assessments" ("requires_follow_up")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_risk_level" ON "risk_assessments" ("risk_level")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_superseded_by_id" ON "risk_assessments" ("superseded_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_valid_until" ON "risk_assessments" ("valid_until")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "risk_assessments" ("id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at")
                     SELECT "id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at" FROM "arisk_assessments"[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE "arisk_assessments"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.2ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "arisk_assessments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "assessed_by_id" integer, "assessment_type" varchar NOT NULL, "risk_level" varchar NOT NULL, "risk_score" decimal(5,2) NOT NULL, "risk_factors" json, "protective_factors" json, "clinical_notes" text, "recommendations" json, "assessed_at" datetime(6) NOT NULL, "valid_until" datetime(6), "requires_follow_up" boolean DEFAULT 0, "next_assessment_due" datetime(6), "assessment_method" varchar, "assessment_data" json, "status" integer DEFAULT 0, "superseded_by_id" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_valid_until" ON "arisk_assessments" ("valid_until")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_superseded_by_id" ON "arisk_assessments" ("superseded_by_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_risk_level" ON "arisk_assessments" ("risk_level")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_requires_follow_up" ON "arisk_assessments" ("requires_follow_up")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_patient_id" ON "arisk_assessments" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tidx_on_patient_id_assessment_type_assessed_at_c1a3b8d791" ON "arisk_assessments" ("patient_id", "assessment_type", "assessed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_next_assessment_due" ON "arisk_assessments" ("next_assessment_due")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_assessment_type" ON "arisk_assessments" ("assessment_type")[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_assessed_by_id" ON "arisk_assessments" ("assessed_by_id")[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_assessed_at" ON "arisk_assessments" ("assessed_at")[0m
  [1m[35mSQL (0.2ms)[0m  [1m[32mINSERT INTO "arisk_assessments" ("id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at")
                     SELECT "id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at" FROM "risk_assessments"[0m
  [1m[35m (0.5ms)[0m  [1m[35mDROP TABLE "risk_assessments"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "risk_assessments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "assessed_by_id" integer, "assessment_type" varchar NOT NULL, "risk_level" varchar NOT NULL, "risk_score" decimal(5,2) NOT NULL, "risk_factors" json, "protective_factors" json, "clinical_notes" text, "recommendations" json, "assessed_at" datetime(6) NOT NULL, "valid_until" datetime(6), "requires_follow_up" boolean DEFAULT 0, "next_assessment_due" datetime(6), "assessment_method" varchar, "assessment_data" json, "status" integer DEFAULT 0, "superseded_by_id" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_94a65cd14d"
FOREIGN KEY ("assessed_by_id")
  REFERENCES "doctors" ("id")
, CONSTRAINT "fk_rails_6abf1d6d6d"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessed_at" ON "risk_assessments" ("assessed_at")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessed_by_id" ON "risk_assessments" ("assessed_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessment_type" ON "risk_assessments" ("assessment_type")[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_next_assessment_due" ON "risk_assessments" ("next_assessment_due")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "idx_on_patient_id_assessment_type_assessed_at_c1a3b8d791" ON "risk_assessments" ("patient_id", "assessment_type", "assessed_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_patient_id" ON "risk_assessments" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_requires_follow_up" ON "risk_assessments" ("requires_follow_up")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_risk_level" ON "risk_assessments" ("risk_level")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_superseded_by_id" ON "risk_assessments" ("superseded_by_id")[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_valid_until" ON "risk_assessments" ("valid_until")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "risk_assessments" ("id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at")
                     SELECT "id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at" FROM "arisk_assessments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "arisk_assessments"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "arisk_assessments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "assessed_by_id" integer, "assessment_type" varchar NOT NULL, "risk_level" varchar NOT NULL, "risk_score" decimal(5,2) NOT NULL, "risk_factors" json, "protective_factors" json, "clinical_notes" text, "recommendations" json, "assessed_at" datetime(6) NOT NULL, "valid_until" datetime(6), "requires_follow_up" boolean DEFAULT 0, "next_assessment_due" datetime(6), "assessment_method" varchar, "assessment_data" json, "status" integer DEFAULT 0, "superseded_by_id" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_valid_until" ON "arisk_assessments" ("valid_until")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_superseded_by_id" ON "arisk_assessments" ("superseded_by_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_risk_level" ON "arisk_assessments" ("risk_level")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_requires_follow_up" ON "arisk_assessments" ("requires_follow_up")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_patient_id" ON "arisk_assessments" ("patient_id")[0m
  [1m[35m (2.2ms)[0m  [1m[35mCREATE INDEX "tidx_on_patient_id_assessment_type_assessed_at_c1a3b8d791" ON "arisk_assessments" ("patient_id", "assessment_type", "assessed_at")[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_next_assessment_due" ON "arisk_assessments" ("next_assessment_due")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_assessment_type" ON "arisk_assessments" ("assessment_type")[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_assessed_by_id" ON "arisk_assessments" ("assessed_by_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_arisk_assessments_on_assessed_at" ON "arisk_assessments" ("assessed_at")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "arisk_assessments" ("id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at")
                     SELECT "id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at" FROM "risk_assessments"[0m
  [1m[35m (0.4ms)[0m  [1m[35mDROP TABLE "risk_assessments"[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE TABLE "risk_assessments" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "patient_id" integer NOT NULL, "assessed_by_id" integer, "assessment_type" varchar NOT NULL, "risk_level" varchar NOT NULL, "risk_score" decimal(5,2) NOT NULL, "risk_factors" json, "protective_factors" json, "clinical_notes" text, "recommendations" json, "assessed_at" datetime(6) NOT NULL, "valid_until" datetime(6), "requires_follow_up" boolean DEFAULT 0, "next_assessment_due" datetime(6), "assessment_method" varchar, "assessment_data" json, "status" integer DEFAULT 0, "superseded_by_id" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_6abf1d6d6d"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
, CONSTRAINT "fk_rails_94a65cd14d"
FOREIGN KEY ("assessed_by_id")
  REFERENCES "doctors" ("id")
, CONSTRAINT "fk_rails_129ec8ae92"
FOREIGN KEY ("superseded_by_id")
  REFERENCES "risk_assessments" ("id")
)[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessed_at" ON "risk_assessments" ("assessed_at")[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessed_by_id" ON "risk_assessments" ("assessed_by_id")[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_assessment_type" ON "risk_assessments" ("assessment_type")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_next_assessment_due" ON "risk_assessments" ("next_assessment_due")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "idx_on_patient_id_assessment_type_assessed_at_c1a3b8d791" ON "risk_assessments" ("patient_id", "assessment_type", "assessed_at")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_patient_id" ON "risk_assessments" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_requires_follow_up" ON "risk_assessments" ("requires_follow_up")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_risk_level" ON "risk_assessments" ("risk_level")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_superseded_by_id" ON "risk_assessments" ("superseded_by_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_risk_assessments_on_valid_until" ON "risk_assessments" ("valid_until")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "risk_assessments" ("id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at")
                     SELECT "id","patient_id","assessed_by_id","assessment_type","risk_level","risk_score","risk_factors","protective_factors","clinical_notes","recommendations","assessed_at","valid_until","requires_follow_up","next_assessment_due","assessment_method","assessment_data","status","superseded_by_id","created_at","updated_at" FROM "arisk_assessments"[0m
  [1m[35m (0.3ms)[0m  [1m[35mDROP TABLE "arisk_assessments"[0m
  [1m[35m (0.1ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.2ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.1ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "asession_participants" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "group_session_id" integer NOT NULL, "user_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "registered_at" datetime(6) NOT NULL, "joined_at" datetime(6), "left_at" datetime(6), "total_duration_seconds" integer DEFAULT 0, "attended" boolean DEFAULT 0, "attendance_percentage" decimal(5,2) DEFAULT 0.0, "feedback" text, "rating" decimal(3,2), "feedback_submitted_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "tindex_asession_participants_on_user_id" ON "asession_participants" ("user_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_asession_participants_on_status" ON "asession_participants" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_asession_participants_on_registered_at" ON "asession_participants" ("registered_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_asession_participants_on_group_session_id" ON "asession_participants" ("group_session_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_asession_participants_on_group_session_id_and_user_id" ON "asession_participants" ("group_session_id", "user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_asession_participants_on_attended" ON "asession_participants" ("attended")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "asession_participants" ("id","group_session_id","user_id","status","registered_at","joined_at","left_at","total_duration_seconds","attended","attendance_percentage","feedback","rating","feedback_submitted_at","created_at","updated_at")
                     SELECT "id","group_session_id","user_id","status","registered_at","joined_at","left_at","total_duration_seconds","attended","attendance_percentage","feedback","rating","feedback_submitted_at","created_at","updated_at" FROM "session_participants"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "session_participants"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "session_participants" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "group_session_id" integer NOT NULL, "user_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "registered_at" datetime(6) NOT NULL, "joined_at" datetime(6), "left_at" datetime(6), "total_duration_seconds" integer DEFAULT 0, "attended" boolean DEFAULT 0, "attendance_percentage" decimal(5,2) DEFAULT 0.0, "feedback" text, "rating" decimal(3,2), "feedback_submitted_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_f197fe06a4"
FOREIGN KEY ("group_session_id")
  REFERENCES "group_sessions" ("id")
)[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_attended" ON "session_participants" ("attended")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_session_participants_on_group_session_id_and_user_id" ON "session_participants" ("group_session_id", "user_id")[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_group_session_id" ON "session_participants" ("group_session_id")[0m
  [1m[35m (0.7ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_registered_at" ON "session_participants" ("registered_at")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_status" ON "session_participants" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_user_id" ON "session_participants" ("user_id")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "session_participants" ("id","group_session_id","user_id","status","registered_at","joined_at","left_at","total_duration_seconds","attended","attendance_percentage","feedback","rating","feedback_submitted_at","created_at","updated_at")
                     SELECT "id","group_session_id","user_id","status","registered_at","joined_at","left_at","total_duration_seconds","attended","attendance_percentage","feedback","rating","feedback_submitted_at","created_at","updated_at" FROM "asession_participants"[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE "asession_participants"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.4ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.2ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "asession_participants" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "group_session_id" integer NOT NULL, "user_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "registered_at" datetime(6) NOT NULL, "joined_at" datetime(6), "left_at" datetime(6), "total_duration_seconds" integer DEFAULT 0, "attended" boolean DEFAULT 0, "attendance_percentage" decimal(5,2) DEFAULT 0.0, "feedback" text, "rating" decimal(3,2), "feedback_submitted_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_asession_participants_on_user_id" ON "asession_participants" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_asession_participants_on_status" ON "asession_participants" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_asession_participants_on_registered_at" ON "asession_participants" ("registered_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_asession_participants_on_group_session_id" ON "asession_participants" ("group_session_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_asession_participants_on_group_session_id_and_user_id" ON "asession_participants" ("group_session_id", "user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_asession_participants_on_attended" ON "asession_participants" ("attended")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "asession_participants" ("id","group_session_id","user_id","status","registered_at","joined_at","left_at","total_duration_seconds","attended","attendance_percentage","feedback","rating","feedback_submitted_at","created_at","updated_at")
                     SELECT "id","group_session_id","user_id","status","registered_at","joined_at","left_at","total_duration_seconds","attended","attendance_percentage","feedback","rating","feedback_submitted_at","created_at","updated_at" FROM "session_participants"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "session_participants"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "session_participants" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "group_session_id" integer NOT NULL, "user_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "registered_at" datetime(6) NOT NULL, "joined_at" datetime(6), "left_at" datetime(6), "total_duration_seconds" integer DEFAULT 0, "attended" boolean DEFAULT 0, "attendance_percentage" decimal(5,2) DEFAULT 0.0, "feedback" text, "rating" decimal(3,2), "feedback_submitted_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_f197fe06a4"
FOREIGN KEY ("group_session_id")
  REFERENCES "group_sessions" ("id")
, CONSTRAINT "fk_rails_1d0ee5bc82"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_attended" ON "session_participants" ("attended")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_session_participants_on_group_session_id_and_user_id" ON "session_participants" ("group_session_id", "user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_group_session_id" ON "session_participants" ("group_session_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_registered_at" ON "session_participants" ("registered_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_status" ON "session_participants" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_session_participants_on_user_id" ON "session_participants" ("user_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "session_participants" ("id","group_session_id","user_id","status","registered_at","joined_at","left_at","total_duration_seconds","attended","attendance_percentage","feedback","rating","feedback_submitted_at","created_at","updated_at")
                     SELECT "id","group_session_id","user_id","status","registered_at","joined_at","left_at","total_duration_seconds","attended","attendance_percentage","feedback","rating","feedback_submitted_at","created_at","updated_at" FROM "asession_participants"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "asession_participants"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "atier_features" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "membership_tier_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "limit_value" integer, "is_unlimited" boolean DEFAULT 0, "is_included" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_atier_features_on_membership_tier_id" ON "atier_features" ("membership_tier_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_atier_features_on_tier_and_feature" ON "atier_features" ("membership_tier_id", "membership_feature_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_atier_features_on_membership_feature_id" ON "atier_features" ("membership_feature_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_atier_features_on_is_unlimited" ON "atier_features" ("is_unlimited")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_atier_features_on_is_included" ON "atier_features" ("is_included")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "atier_features" ("id","membership_tier_id","membership_feature_id","limit_value","is_unlimited","is_included","created_at","updated_at")
                     SELECT "id","membership_tier_id","membership_feature_id","limit_value","is_unlimited","is_included","created_at","updated_at" FROM "tier_features"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "tier_features"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "tier_features" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "membership_tier_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "limit_value" integer, "is_unlimited" boolean DEFAULT 0, "is_included" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_605fb37803"
FOREIGN KEY ("membership_feature_id")
  REFERENCES "membership_features" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_is_included" ON "tier_features" ("is_included")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_is_unlimited" ON "tier_features" ("is_unlimited")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_membership_feature_id" ON "tier_features" ("membership_feature_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_tier_features_on_tier_and_feature" ON "tier_features" ("membership_tier_id", "membership_feature_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_membership_tier_id" ON "tier_features" ("membership_tier_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "tier_features" ("id","membership_tier_id","membership_feature_id","limit_value","is_unlimited","is_included","created_at","updated_at")
                     SELECT "id","membership_tier_id","membership_feature_id","limit_value","is_unlimited","is_included","created_at","updated_at" FROM "atier_features"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "atier_features"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "atier_features" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "membership_tier_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "limit_value" integer, "is_unlimited" boolean DEFAULT 0, "is_included" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_atier_features_on_membership_tier_id" ON "atier_features" ("membership_tier_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_atier_features_on_tier_and_feature" ON "atier_features" ("membership_tier_id", "membership_feature_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_atier_features_on_membership_feature_id" ON "atier_features" ("membership_feature_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_atier_features_on_is_unlimited" ON "atier_features" ("is_unlimited")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_atier_features_on_is_included" ON "atier_features" ("is_included")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "atier_features" ("id","membership_tier_id","membership_feature_id","limit_value","is_unlimited","is_included","created_at","updated_at")
                     SELECT "id","membership_tier_id","membership_feature_id","limit_value","is_unlimited","is_included","created_at","updated_at" FROM "tier_features"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "tier_features"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "tier_features" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "membership_tier_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "limit_value" integer, "is_unlimited" boolean DEFAULT 0, "is_included" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_605fb37803"
FOREIGN KEY ("membership_feature_id")
  REFERENCES "membership_features" ("id")
, CONSTRAINT "fk_rails_39701094c9"
FOREIGN KEY ("membership_tier_id")
  REFERENCES "membership_tiers" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_is_included" ON "tier_features" ("is_included")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_is_unlimited" ON "tier_features" ("is_unlimited")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_membership_feature_id" ON "tier_features" ("membership_feature_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_tier_features_on_tier_and_feature" ON "tier_features" ("membership_tier_id", "membership_feature_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_tier_features_on_membership_tier_id" ON "tier_features" ("membership_tier_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "tier_features" ("id","membership_tier_id","membership_feature_id","limit_value","is_unlimited","is_included","created_at","updated_at")
                     SELECT "id","membership_tier_id","membership_feature_id","limit_value","is_unlimited","is_included","created_at","updated_at" FROM "atier_features"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "atier_features"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "ausage_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "period_start" date NOT NULL, "period_end" date NOT NULL, "usage_count" integer DEFAULT 0, "limit_exceeded" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ausage_trackers_on_user_id" ON "ausage_trackers" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_ausage_trackers_unique" ON "ausage_trackers" ("user_id", "membership_feature_id", "period_start")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ausage_trackers_on_period_start" ON "ausage_trackers" ("period_start")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ausage_trackers_on_membership_feature_id" ON "ausage_trackers" ("membership_feature_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_ausage_trackers_on_limit_exceeded" ON "ausage_trackers" ("limit_exceeded")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "ausage_trackers" ("id","user_id","membership_feature_id","period_start","period_end","usage_count","limit_exceeded","created_at","updated_at")
                     SELECT "id","user_id","membership_feature_id","period_start","period_end","usage_count","limit_exceeded","created_at","updated_at" FROM "usage_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "usage_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "usage_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "period_start" date NOT NULL, "period_end" date NOT NULL, "usage_count" integer DEFAULT 0, "limit_exceeded" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_4cba118828"
FOREIGN KEY ("membership_feature_id")
  REFERENCES "membership_features" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_limit_exceeded" ON "usage_trackers" ("limit_exceeded")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_membership_feature_id" ON "usage_trackers" ("membership_feature_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_period_start" ON "usage_trackers" ("period_start")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_usage_trackers_unique" ON "usage_trackers" ("user_id", "membership_feature_id", "period_start")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_user_id" ON "usage_trackers" ("user_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "usage_trackers" ("id","user_id","membership_feature_id","period_start","period_end","usage_count","limit_exceeded","created_at","updated_at")
                     SELECT "id","user_id","membership_feature_id","period_start","period_end","usage_count","limit_exceeded","created_at","updated_at" FROM "ausage_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "ausage_trackers"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "ausage_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "period_start" date NOT NULL, "period_end" date NOT NULL, "usage_count" integer DEFAULT 0, "limit_exceeded" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_ausage_trackers_on_user_id" ON "ausage_trackers" ("user_id")[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_ausage_trackers_unique" ON "ausage_trackers" ("user_id", "membership_feature_id", "period_start")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_ausage_trackers_on_period_start" ON "ausage_trackers" ("period_start")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_ausage_trackers_on_membership_feature_id" ON "ausage_trackers" ("membership_feature_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_ausage_trackers_on_limit_exceeded" ON "ausage_trackers" ("limit_exceeded")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "ausage_trackers" ("id","user_id","membership_feature_id","period_start","period_end","usage_count","limit_exceeded","created_at","updated_at")
                     SELECT "id","user_id","membership_feature_id","period_start","period_end","usage_count","limit_exceeded","created_at","updated_at" FROM "usage_trackers"[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE "usage_trackers"[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE TABLE "usage_trackers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_feature_id" integer NOT NULL, "period_start" date NOT NULL, "period_end" date NOT NULL, "usage_count" integer DEFAULT 0, "limit_exceeded" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_4cba118828"
FOREIGN KEY ("membership_feature_id")
  REFERENCES "membership_features" ("id")
, CONSTRAINT "fk_rails_d413b57732"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_limit_exceeded" ON "usage_trackers" ("limit_exceeded")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_membership_feature_id" ON "usage_trackers" ("membership_feature_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_period_start" ON "usage_trackers" ("period_start")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_usage_trackers_unique" ON "usage_trackers" ("user_id", "membership_feature_id", "period_start")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_usage_trackers_on_user_id" ON "usage_trackers" ("user_id")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "usage_trackers" ("id","user_id","membership_feature_id","period_start","period_end","usage_count","limit_exceeded","created_at","updated_at")
                     SELECT "id","user_id","membership_feature_id","period_start","period_end","usage_count","limit_exceeded","created_at","updated_at" FROM "ausage_trackers"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "ausage_trackers"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.2ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "auser_memberships" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_tier_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "starts_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "auto_renewal" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_user_id" ON "auser_memberships" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_user_id_and_status" ON "auser_memberships" ("user_id", "status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_status" ON "auser_memberships" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_starts_at" ON "auser_memberships" ("starts_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_membership_tier_id" ON "auser_memberships" ("membership_tier_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_expires_at" ON "auser_memberships" ("expires_at")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "auser_memberships" ("id","user_id","membership_tier_id","status","starts_at","expires_at","auto_renewal","created_at","updated_at")
                     SELECT "id","user_id","membership_tier_id","status","starts_at","expires_at","auto_renewal","created_at","updated_at" FROM "user_memberships"[0m
  [1m[35m (0.2ms)[0m  [1m[35mDROP TABLE "user_memberships"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "user_memberships" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_tier_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "starts_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "auto_renewal" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_a2f75c886b"
FOREIGN KEY ("membership_tier_id")
  REFERENCES "membership_tiers" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_expires_at" ON "user_memberships" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_membership_tier_id" ON "user_memberships" ("membership_tier_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_starts_at" ON "user_memberships" ("starts_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_status" ON "user_memberships" ("status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_user_id_and_status" ON "user_memberships" ("user_id", "status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_user_id" ON "user_memberships" ("user_id")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "user_memberships" ("id","user_id","membership_tier_id","status","starts_at","expires_at","auto_renewal","created_at","updated_at")
                     SELECT "id","user_id","membership_tier_id","status","starts_at","expires_at","auto_renewal","created_at","updated_at" FROM "auser_memberships"[0m
  [1m[35m (0.3ms)[0m  [1m[35mDROP TABLE "auser_memberships"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "auser_memberships" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_tier_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "starts_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "auto_renewal" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_user_id" ON "auser_memberships" ("user_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_user_id_and_status" ON "auser_memberships" ("user_id", "status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_status" ON "auser_memberships" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_starts_at" ON "auser_memberships" ("starts_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_membership_tier_id" ON "auser_memberships" ("membership_tier_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_auser_memberships_on_expires_at" ON "auser_memberships" ("expires_at")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "auser_memberships" ("id","user_id","membership_tier_id","status","starts_at","expires_at","auto_renewal","created_at","updated_at")
                     SELECT "id","user_id","membership_tier_id","status","starts_at","expires_at","auto_renewal","created_at","updated_at" FROM "user_memberships"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "user_memberships"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "user_memberships" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "user_id" integer NOT NULL, "membership_tier_id" integer NOT NULL, "status" integer DEFAULT 0 NOT NULL, "starts_at" datetime(6) NOT NULL, "expires_at" datetime(6) NOT NULL, "auto_renewal" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_a2f75c886b"
FOREIGN KEY ("membership_tier_id")
  REFERENCES "membership_tiers" ("id")
, CONSTRAINT "fk_rails_e670a4dd3b"
FOREIGN KEY ("user_id")
  REFERENCES "users" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_expires_at" ON "user_memberships" ("expires_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_membership_tier_id" ON "user_memberships" ("membership_tier_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_starts_at" ON "user_memberships" ("starts_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_status" ON "user_memberships" ("status")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_user_id_and_status" ON "user_memberships" ("user_id", "status")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_user_memberships_on_user_id" ON "user_memberships" ("user_id")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "user_memberships" ("id","user_id","membership_tier_id","status","starts_at","expires_at","auto_renewal","created_at","updated_at")
                     SELECT "id","user_id","membership_tier_id","status","starts_at","expires_at","auto_renewal","created_at","updated_at" FROM "auser_memberships"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "auser_memberships"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.1ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "avideo_progresses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_video_id" integer NOT NULL, "patient_id" integer NOT NULL, "watched_seconds" integer DEFAULT 0 NOT NULL, "progress_percentage" decimal(5,2) DEFAULT 0.0, "completed" boolean DEFAULT 0, "first_watched_at" datetime(6), "last_watched_at" datetime(6), "completed_at" datetime(6), "watch_count" integer DEFAULT 0, "watch_sessions" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE INDEX "tindex_avideo_progresses_on_progress_percentage" ON "avideo_progresses" ("progress_percentage")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_avideo_progresses_on_patient_id" ON "avideo_progresses" ("patient_id")[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "tindex_avideo_progresses_on_last_watched_at" ON "avideo_progresses" ("last_watched_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_avideo_progresses_on_course_video_id" ON "avideo_progresses" ("course_video_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_avideo_progresses_on_course_video_id_and_patient_id" ON "avideo_progresses" ("course_video_id", "patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_avideo_progresses_on_completed" ON "avideo_progresses" ("completed")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "avideo_progresses" ("id","course_video_id","patient_id","watched_seconds","progress_percentage","completed","first_watched_at","last_watched_at","completed_at","watch_count","watch_sessions","created_at","updated_at")
                     SELECT "id","course_video_id","patient_id","watched_seconds","progress_percentage","completed","first_watched_at","last_watched_at","completed_at","watch_count","watch_sessions","created_at","updated_at" FROM "video_progresses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "video_progresses"[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE TABLE "video_progresses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_video_id" integer NOT NULL, "patient_id" integer NOT NULL, "watched_seconds" integer DEFAULT 0 NOT NULL, "progress_percentage" decimal(5,2) DEFAULT 0.0, "completed" boolean DEFAULT 0, "first_watched_at" datetime(6), "last_watched_at" datetime(6), "completed_at" datetime(6), "watch_count" integer DEFAULT 0, "watch_sessions" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_c494bbcd05"
FOREIGN KEY ("course_video_id")
  REFERENCES "course_videos" ("id")
)[0m
  [1m[35m (0.2ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_completed" ON "video_progresses" ("completed")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_video_progresses_on_course_video_id_and_patient_id" ON "video_progresses" ("course_video_id", "patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_course_video_id" ON "video_progresses" ("course_video_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_last_watched_at" ON "video_progresses" ("last_watched_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_patient_id" ON "video_progresses" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_progress_percentage" ON "video_progresses" ("progress_percentage")[0m
  [1m[35mSQL (0.2ms)[0m  [1m[32mINSERT INTO "video_progresses" ("id","course_video_id","patient_id","watched_seconds","progress_percentage","completed","first_watched_at","last_watched_at","completed_at","watch_count","watch_sessions","created_at","updated_at")
                     SELECT "id","course_video_id","patient_id","watched_seconds","progress_percentage","completed","first_watched_at","last_watched_at","completed_at","watch_count","watch_sessions","created_at","updated_at" FROM "avideo_progresses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "avideo_progresses"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN immediate TRANSACTION[0m
  [1m[35mSQL (0.2ms)[0m  [1m[35mPRAGMA foreign_keys[0m
  [1m[35mSQL (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = ON[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = OFF[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TEMPORARY TABLE "avideo_progresses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_video_id" integer NOT NULL, "patient_id" integer NOT NULL, "watched_seconds" integer DEFAULT 0 NOT NULL, "progress_percentage" decimal(5,2) DEFAULT 0.0, "completed" boolean DEFAULT 0, "first_watched_at" datetime(6), "last_watched_at" datetime(6), "completed_at" datetime(6), "watch_count" integer DEFAULT 0, "watch_sessions" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_avideo_progresses_on_progress_percentage" ON "avideo_progresses" ("progress_percentage")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_avideo_progresses_on_patient_id" ON "avideo_progresses" ("patient_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_avideo_progresses_on_last_watched_at" ON "avideo_progresses" ("last_watched_at")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "tindex_avideo_progresses_on_course_video_id" ON "avideo_progresses" ("course_video_id")[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE UNIQUE INDEX "tindex_avideo_progresses_on_course_video_id_and_patient_id" ON "avideo_progresses" ("course_video_id", "patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "tindex_avideo_progresses_on_completed" ON "avideo_progresses" ("completed")[0m
  [1m[35mSQL (0.1ms)[0m  [1m[32mINSERT INTO "avideo_progresses" ("id","course_video_id","patient_id","watched_seconds","progress_percentage","completed","first_watched_at","last_watched_at","completed_at","watch_count","watch_sessions","created_at","updated_at")
                     SELECT "id","course_video_id","patient_id","watched_seconds","progress_percentage","completed","first_watched_at","last_watched_at","completed_at","watch_count","watch_sessions","created_at","updated_at" FROM "video_progresses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "video_progresses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "video_progresses" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "course_video_id" integer NOT NULL, "patient_id" integer NOT NULL, "watched_seconds" integer DEFAULT 0 NOT NULL, "progress_percentage" decimal(5,2) DEFAULT 0.0, "completed" boolean DEFAULT 0, "first_watched_at" datetime(6), "last_watched_at" datetime(6), "completed_at" datetime(6), "watch_count" integer DEFAULT 0, "watch_sessions" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_c494bbcd05"
FOREIGN KEY ("course_video_id")
  REFERENCES "course_videos" ("id")
, CONSTRAINT "fk_rails_d3cf50c204"
FOREIGN KEY ("patient_id")
  REFERENCES "patients" ("id")
)[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_completed" ON "video_progresses" ("completed")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE UNIQUE INDEX "index_video_progresses_on_course_video_id_and_patient_id" ON "video_progresses" ("course_video_id", "patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_course_video_id" ON "video_progresses" ("course_video_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_last_watched_at" ON "video_progresses" ("last_watched_at")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_patient_id" ON "video_progresses" ("patient_id")[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_video_progresses_on_progress_percentage" ON "video_progresses" ("progress_percentage")[0m
  [1m[35mSQL (0.0ms)[0m  [1m[32mINSERT INTO "video_progresses" ("id","course_video_id","patient_id","watched_seconds","progress_percentage","completed","first_watched_at","last_watched_at","completed_at","watch_count","watch_sessions","created_at","updated_at")
                     SELECT "id","course_video_id","patient_id","watched_seconds","progress_percentage","completed","first_watched_at","last_watched_at","completed_at","watch_count","watch_sessions","created_at","updated_at" FROM "avideo_progresses"[0m
  [1m[35m (0.1ms)[0m  [1m[35mDROP TABLE "avideo_progresses"[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA defer_foreign_keys = 0[0m
  [1m[35m (0.0ms)[0m  [1m[35mPRAGMA foreign_keys = 1[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[36mCOMMIT TRANSACTION[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" varchar NOT NULL PRIMARY KEY)[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[35m (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" (version) VALUES (20250620134544)[0m
  [1m[35m (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" (version) VALUES
(20250620134431),
(20250620132917),
(20250620132848),
(20250620132817),
(20250620132748),
(20250620132720),
(20250620132612),
(20250620132544),
(20250620132516),
(20250620132329),
(20250620132300),
(20250620132231),
(20250620132202),
(20250620132134),
(20250620132059),
(20250620132032),
(20250620132002),
(20250620131933),
(20250620131900),
(20250620131835),
(20250620131807),
(20250620131734),
(20250620131703),
(20250620131618),
(20250620130725),
(20250620130715),
(20250620130706),
(20250620130658),
(20250620130032),
(20250620130022),
(20250620130013),
(20250620125956),
(20250620125947),
(20250620125518),
(20250620125511),
(20250620125502),
(20250620125455);[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" varchar NOT NULL PRIMARY KEY, "value" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL)[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = ? ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1[0m  [[nil, "environment"]]
  [1m[36mActiveRecord::InternalMetadata Create (0.1ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'test', '2025-06-20 16:35:04.843318', '2025-06-20 16:35:04.843320') RETURNING "key"[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.0ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = ? ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1[0m  [[nil, "environment"]]
  [1m[36mActiveRecord::InternalMetadata Load (0.0ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = ? ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1[0m  [[nil, "schema_sha1"]]
  [1m[36mActiveRecord::InternalMetadata Create (0.1ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('schema_sha1', '9855d381d367a8d7cf13b98cbd7cbf54aa156a44', '2025-06-20 16:35:04.844451', '2025-06-20 16:35:04.844452') RETURNING "key"[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.2ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = ? ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1[0m  [[nil, "schema_sha1"]]
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.1ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = ? ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1[0m  [[nil, "schema_sha1"]]
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[36mBEGIN deferred TRANSACTION[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[31mROLLBACK TRANSACTION[0m
